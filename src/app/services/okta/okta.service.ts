import { APIs } from 'src/app/constants/apis';
import { environment } from 'src/environments/environment';
import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable()
export class OktaService {
  private oktaConfig = {
    clientId: environment.oktaClientId,
    issuer: environment.oktaIssuer,
    redirectUri: this.getOktaRedirectUri(),
    useClassicEngine: true,
    scopes: ['openid', 'profile', 'email', 'offline_access'],
    postLogoutRedirectUri: this.getOktaRedirectUri(true),
    testing: {
      disableHttpsCheck: false
    },
    authParams: {
      responseType: ['code'],
      pkce: true,
      display: 'page',
      prompt: 'none',
      loggingLevel: 'debug'
    },
    // Enable automatic token renewal
    tokenManager: {
      autoRenew: true,
      autoRemove: false,
      secure: true,
      storage: 'localStorage',
      expireEarlySeconds: 120,
      syncStorage: true
    }
  };

  constructor(private platform: Platform) {}

  getOktaRedirectUri(isLogout = false): string {
    let platformURL;

    if (this.platform.is('capacitor')) {
      platformURL = this.platform.is('ios') ? APIs.redirectURLIos : APIs.redirectURLAndroid;
    } else {
      platformURL = environment.appUrl;
    }

    return isLogout ? `${platformURL}/login` : `${platformURL}${environment.oktaRedirectUri}`;
  }

  getOktaConfig(): any {
    return this.oktaConfig;
  }
}
