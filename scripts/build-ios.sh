#!/bin/bash

# iOS Build Script for CitusHealth Mobile App
# This script helps build the iOS app locally for testing before CI/CD

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="qa6"
BUILD_TYPE="development"
CLEAN_BUILD=false
SKIP_TESTS=false
OPEN_XCODE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Build environment (qa6, qa7, qa8, stage, production) [default: qa6]"
    echo "  -t, --type TYPE         Build type (development, adhoc, appstore) [default: development]"
    echo "  -c, --clean             Clean build (removes node_modules, www, ios/App/Pods)"
    echo "  -s, --skip-tests        Skip running unit tests"
    echo "  -x, --open-xcode        Open Xcode after build"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Build qa6 development"
    echo "  $0 -e production -t appstore         # Build production for App Store"
    echo "  $0 -c -e qa7 -x                     # Clean build qa7 and open Xcode"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -s|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -x|--open-xcode)
            OPEN_XCODE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
case $ENVIRONMENT in
    qa6|qa7|qa8|stage|production)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_error "Valid environments: qa6, qa7, qa8, stage, production"
        exit 1
        ;;
esac

# Validate build type
case $BUILD_TYPE in
    development|adhoc|appstore)
        ;;
    *)
        print_error "Invalid build type: $BUILD_TYPE"
        print_error "Valid build types: development, adhoc, appstore"
        exit 1
        ;;
esac

print_status "Starting iOS build process..."
print_status "Environment: $ENVIRONMENT"
print_status "Build Type: $BUILD_TYPE"
print_status "Clean Build: $CLEAN_BUILD"

# Check if we're in the right directory
if [[ ! -f "package.json" ]] || [[ ! -f "capacitor.config.ts" ]]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check required tools
print_status "Checking required tools..."

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

if ! command -v xcodebuild &> /dev/null; then
    print_error "Xcode command line tools are not installed"
    print_error "Run: xcode-select --install"
    exit 1
fi

if ! command -v pod &> /dev/null; then
    print_error "CocoaPods is not installed"
    print_error "Run: sudo gem install cocoapods"
    exit 1
fi

print_success "All required tools are available"

# Clean build if requested
if [[ "$CLEAN_BUILD" == true ]]; then
    print_status "Performing clean build..."
    
    if [[ -d "node_modules" ]]; then
        print_status "Removing node_modules..."
        rm -rf node_modules
    fi
    
    if [[ -d "www" ]]; then
        print_status "Removing www..."
        rm -rf www
    fi
    
    if [[ -d "ios/App/Pods" ]]; then
        print_status "Removing iOS Pods..."
        rm -rf ios/App/Pods
        rm -f ios/App/Podfile.lock
    fi
    
    print_status "Cleaning npm cache..."
    npm cache clean --force
fi

# Install dependencies
print_status "Installing Node.js dependencies..."
npm install --force

# Install global tools if not present
if ! command -v ionic &> /dev/null; then
    print_status "Installing Ionic CLI..."
    npm install -g @ionic/cli
fi

if ! command -v cap &> /dev/null; then
    print_status "Installing Capacitor CLI..."
    npm install -g @capacitor/cli
fi

# Run tests unless skipped
if [[ "$SKIP_TESTS" != true ]]; then
    print_status "Running unit tests..."
    if npm run test:coverage:ci; then
        print_success "Unit tests passed"
    else
        print_warning "Unit tests failed, but continuing with build..."
    fi
fi

# Build web app
print_status "Building web application for $ENVIRONMENT..."
if ionic build --configuration=$ENVIRONMENT; then
    print_success "Web build completed successfully"
else
    print_error "Web build failed"
    exit 1
fi

# Sync Capacitor
print_status "Syncing Capacitor..."
if npx cap sync ios; then
    print_success "Capacitor sync completed"
else
    print_error "Capacitor sync failed"
    exit 1
fi

# Install iOS dependencies
print_status "Installing iOS dependencies (CocoaPods)..."
cd ios/App
if pod install --repo-update; then
    print_success "CocoaPods installation completed"
else
    print_error "CocoaPods installation failed"
    exit 1
fi
cd ../..

# Build iOS app based on type
print_status "Building iOS application..."

case $BUILD_TYPE in
    development)
        print_status "Building for iOS Simulator (Development)..."
        cd ios/App
        if xcodebuild -workspace App.xcworkspace \
            -scheme App \
            -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest' \
            -configuration Debug \
            build; then
            print_success "iOS development build completed successfully"
        else
            print_error "iOS development build failed"
            exit 1
        fi
        cd ../..
        ;;
    adhoc|appstore)
        print_warning "Archive builds require proper code signing setup"
        print_status "Building iOS Archive..."
        cd ios/App
        CONFIGURATION=$([[ "$BUILD_TYPE" == "appstore" ]] && echo "Release" || echo "Debug")
        if xcodebuild -workspace App.xcworkspace \
            -scheme App \
            -configuration $CONFIGURATION \
            -destination 'generic/platform=iOS' \
            -archivePath App.xcarchive \
            archive; then
            print_success "iOS archive build completed successfully"
            print_status "Archive location: ios/App/App.xcarchive"
        else
            print_error "iOS archive build failed"
            print_error "Make sure you have proper code signing certificates installed"
            exit 1
        fi
        cd ../..
        ;;
esac

# Open Xcode if requested
if [[ "$OPEN_XCODE" == true ]]; then
    print_status "Opening Xcode..."
    open ios/App/App.xcworkspace
fi

print_success "iOS build process completed successfully!"
print_status "Summary:"
print_status "  Environment: $ENVIRONMENT"
print_status "  Build Type: $BUILD_TYPE"
print_status "  Web Build: www/"
print_status "  iOS Project: ios/App/App.xcworkspace"

if [[ "$BUILD_TYPE" != "development" ]]; then
    print_status "  iOS Archive: ios/App/App.xcarchive"
fi

print_status ""
print_status "Next steps:"
if [[ "$BUILD_TYPE" == "development" ]]; then
    print_status "  1. Open Xcode: open ios/App/App.xcworkspace"
    print_status "  2. Select a simulator and run the app"
else
    print_status "  1. Open Xcode: open ios/App/App.xcworkspace"
    print_status "  2. Use the archive to create an IPA or upload to App Store Connect"
fi
