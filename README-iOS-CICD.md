# iOS CI/CD Pipeline for CitusHealth Mobile App

This repository includes a comprehensive CI/CD pipeline for building and deploying the iOS version of the CitusHealth mobile application using GitHub Actions.

## 🚀 Quick Start

### 1. Test Your Setup
Before setting up the full pipeline, test your configuration:

```bash
# Go to GitHub Actions → iOS Build Test → Run workflow
# Select environment: qa6
# Select test type: web-only (start simple)
```

### 2. Local Development
Use the provided script to build locally:

```bash
# Basic build
./scripts/build-ios.sh

# Build for specific environment
./scripts/build-ios.sh -e qa6 -x

# Clean build with tests
./scripts/build-ios.sh -c -e production -t appstore
```

### 3. Full CI/CD Pipeline
Once tested, use the main pipeline:

```bash
# Go to GitHub Actions → iOS CI/CD Pipeline → Run workflow
# Configure environment and build type as needed
```

## 📁 Files Added

### GitHub Actions Workflows
- `.github/workflows/ios-ci-cd.yml` - Main CI/CD pipeline
- `.github/workflows/ios-test.yml` - Testing and validation workflow

### Documentation
- `docs/ios-ci-cd-setup.md` - Comprehensive setup guide
- `README-iOS-CICD.md` - This file

### Scripts
- `scripts/build-ios.sh` - Local build script

## 🔧 Pipeline Features

### Automated Triggers
- **Push** to `main`, `develop`, `release/*` branches
- **Pull Requests** to `main`, `develop` branches
- **Manual dispatch** with custom parameters

### Build Configurations
- **Environments**: qa6, qa7, qa8, stage, production
- **Build Types**: development, adhoc, appstore
- **Platforms**: iOS Simulator, iOS Device, App Store

### Pipeline Jobs
1. **Web Build & Test** - Angular/Ionic app build with unit tests
2. **iOS Build** - Native iOS app build with Xcode
3. **App Store Deploy** - Automatic upload to App Store Connect
4. **Notifications** - Success/failure notifications

## 🛠️ Setup Requirements

### Prerequisites
- Apple Developer Program membership
- Xcode 15.2+ (handled by GitHub Actions)
- Node.js 18.x (handled by GitHub Actions)
- CocoaPods (handled by GitHub Actions)

### GitHub Secrets
Configure these secrets in your repository:

| Secret | Description | Required For |
|--------|-------------|--------------|
| `NPM_AUTH_TOKEN` | GitHub Packages access | All builds |
| `IOS_CERTIFICATES_P12` | Code signing certificate | Archive builds |
| `IOS_CERTIFICATES_PASSWORD` | Certificate password | Archive builds |
| `IOS_TEAM_ID` | Apple Developer Team ID | Archive builds |
| `APPSTORE_ISSUER_ID` | App Store Connect API | App Store uploads |
| `APPSTORE_KEY_ID` | App Store Connect Key ID | App Store uploads |
| `APPSTORE_PRIVATE_KEY` | App Store Connect Private Key | App Store uploads |
| `APPSTORE_USERNAME` | Apple ID | App Store uploads |
| `APPSTORE_PASSWORD` | App-specific password | App Store uploads |

## 📱 Build Types Explained

### Development Build
- **Purpose**: Testing and development
- **Target**: iOS Simulator
- **Code Signing**: Not required
- **Output**: Xcode build (no IPA)
- **Speed**: Fast ⚡

### Ad Hoc Build
- **Purpose**: Internal testing on devices
- **Target**: iOS Devices
- **Code Signing**: Development/Ad Hoc profile required
- **Output**: IPA file
- **Speed**: Medium 🔄

### App Store Build
- **Purpose**: App Store submission
- **Target**: App Store Connect
- **Code Signing**: Distribution certificate required
- **Output**: IPA file + automatic upload
- **Speed**: Slow but complete 🎯

## 🌍 Environment Configurations

Each environment uses different configuration files:

- **qa6** → `src/environments/environment.qa6.ts`
- **qa7** → `src/environments/environment.qa7.ts`
- **qa8** → `src/environments/environment.qa8.ts`
- **stage** → `src/environments/environment.stage.ts`
- **production** → `src/environments/environment.prod.ts`

## 🔍 Testing Strategy

### 1. Start Simple
```bash
# Test web build only
GitHub Actions → iOS Build Test → web-only
```

### 2. Test iOS Setup
```bash
# Test Capacitor and CocoaPods
GitHub Actions → iOS Build Test → ios-simulator
```

### 3. Full Integration Test
```bash
# Test complete pipeline
GitHub Actions → iOS Build Test → full-test
```

### 4. Production Pipeline
```bash
# Use main pipeline
GitHub Actions → iOS CI/CD Pipeline
```

## 🚨 Troubleshooting

### Common Issues

#### 1. NPM Authentication Failed
```bash
# Check if NPM_AUTH_TOKEN secret is set correctly
# Verify token has packages:read permission
```

#### 2. CocoaPods Installation Failed
```bash
# Usually resolves automatically with repo-update
# Check iOS dependencies in package.json
```

#### 3. Code Signing Failed
```bash
# Verify certificates are valid and not expired
# Check provisioning profiles match bundle ID
# Ensure Team ID is correct
```

#### 4. Capacitor Sync Failed
```bash
# Check capacitor.config.ts configuration
# Verify iOS platform is properly installed
# Try: npx cap doctor
```

### Debug Locally

```bash
# Test web build
ionic build --configuration=qa6

# Test Capacitor sync
npx cap sync ios

# Test iOS build
cd ios/App
xcodebuild -workspace App.xcworkspace -scheme App -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest' build
```

## 📊 Monitoring

### Build Artifacts
- **Web builds** - Available for 7 days
- **IPA files** - Available for 30 days
- **Test coverage** - Available for 7 days

### Notifications
- GitHub Actions status badges
- Email notifications (GitHub settings)
- Can be extended with Slack, Teams, etc.

## 🔄 Workflow Examples

### Development Workflow
```bash
1. Create feature branch
2. Make changes
3. Push → Automatic development build
4. Create PR → Automatic testing
5. Merge → Automatic qa6 build
```

### Release Workflow
```bash
1. Manual trigger: iOS CI/CD Pipeline
2. Environment: production
3. Build type: appstore
4. Automatic upload to App Store Connect
5. Manual review and release in App Store Connect
```

## 📚 Additional Resources

- [Detailed Setup Guide](docs/ios-ci-cd-setup.md)
- [Apple Developer Documentation](https://developer.apple.com/documentation/)
- [Capacitor iOS Documentation](https://capacitorjs.com/docs/ios)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

## 🤝 Contributing

When contributing to the iOS CI/CD pipeline:

1. Test changes with the test workflow first
2. Update documentation if needed
3. Ensure backward compatibility
4. Test with different environments

## 📞 Support

For issues with the iOS CI/CD pipeline:

1. Check the troubleshooting section above
2. Review workflow logs in GitHub Actions
3. Test locally using the provided script
4. Contact the development team

---

**Happy Building! 🎉**
