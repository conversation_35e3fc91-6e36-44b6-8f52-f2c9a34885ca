#!/bin/bash

# Script to prepare iOS certificates and provisioning profiles for GitHub Secrets
# This script helps convert your files to the format needed for CI/CD

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================================="
    echo "$1"
    echo "=================================================="
}

# Check if files exist
P12_FILE="13-6cert 2.p12"
PROVISION_FILE="CitusHealth_Stage_Provisioning.mobileprovision"

print_header "iOS CI/CD Secrets Preparation"

print_status "Looking for your iOS files..."

# Check P12 certificate
if [[ -f "$P12_FILE" ]]; then
    print_success "Found P12 certificate: $P12_FILE"
else
    print_error "P12 certificate not found: $P12_FILE"
    print_status "Please make sure the file is in the current directory"
    exit 1
fi

# Check provisioning profile
if [[ -f "$PROVISION_FILE" ]]; then
    print_success "Found provisioning profile: $PROVISION_FILE"
else
    print_error "Provisioning profile not found: $PROVISION_FILE"
    print_status "Please make sure the file is in the current directory"
    exit 1
fi

print_header "Converting Files to Base64"

# Convert P12 to base64
print_status "Converting P12 certificate to base64..."
P12_BASE64=$(base64 -i "$P12_FILE")
print_success "P12 certificate converted successfully"

# Convert provisioning profile to base64
print_status "Converting provisioning profile to base64..."
PROVISION_BASE64=$(base64 -i "$PROVISION_FILE")
print_success "Provisioning profile converted successfully"

print_header "Extracting Provisioning Profile Information"

# Extract info from provisioning profile
print_status "Extracting information from provisioning profile..."
PROVISION_INFO=$(security cms -D -i "$PROVISION_FILE" 2>/dev/null)

if [[ $? -eq 0 ]]; then
    print_success "Provisioning profile information extracted"
    
    # Extract Team ID
    TEAM_ID=$(echo "$PROVISION_INFO" | grep -A1 "TeamIdentifier" | tail -1 | sed 's/.*<string>\(.*\)<\/string>.*/\1/' | tr -d '\t ')
    
    # Extract Bundle ID
    BUNDLE_ID=$(echo "$PROVISION_INFO" | grep -A1 "application-identifier" | tail -1 | sed 's/.*<string>[^.]*\.\(.*\)<\/string>.*/\1/' | tr -d '\t ')
    
    # Extract expiration date
    EXPIRY_DATE=$(echo "$PROVISION_INFO" | grep -A1 "ExpirationDate" | tail -1 | sed 's/.*<date>\(.*\)<\/date>.*/\1/' | tr -d '\t ')
    
    print_status "Team ID: $TEAM_ID"
    print_status "Bundle ID: $BUNDLE_ID"
    print_status "Expires: $EXPIRY_DATE"
else
    print_warning "Could not extract provisioning profile information"
    print_warning "You may need to manually find your Team ID"
fi

print_header "GitHub Secrets Configuration"

echo ""
print_status "Add these secrets to your GitHub repository:"
print_status "Go to: Settings → Secrets and variables → Actions → New repository secret"
echo ""

echo "1. IOS_CERTIFICATES_P12"
echo "   Value: $P12_BASE64"
echo ""

echo "2. IOS_CERTIFICATES_PASSWORD"
echo "   Value: [Enter the password you used when exporting the P12 file]"
echo ""

if [[ -n "$TEAM_ID" ]]; then
    echo "3. IOS_TEAM_ID"
    echo "   Value: $TEAM_ID"
    echo ""
fi

echo "4. IOS_STAGE_PROVISIONING_PROFILE"
echo "   Value: $PROVISION_BASE64"
echo ""

print_header "Additional Setup Required"

print_warning "You still need to configure these secrets for App Store deployment:"
echo ""
echo "5. APPSTORE_ISSUER_ID"
echo "   - Go to App Store Connect → Users and Access → Keys"
echo "   - Create API key and note the Issuer ID"
echo ""
echo "6. APPSTORE_KEY_ID"
echo "   - The Key ID from the API key you created"
echo ""
echo "7. APPSTORE_PRIVATE_KEY"
echo "   - Download the .p8 file and paste its contents"
echo ""
echo "8. APPSTORE_USERNAME"
echo "   - Your Apple ID email"
echo ""
echo "9. APPSTORE_PASSWORD"
echo "   - Generate app-specific password at appleid.apple.com"
echo ""

print_header "Verification Steps"

print_status "After adding secrets to GitHub, you can test with:"
echo ""
echo "1. Go to GitHub Actions → iOS Build Test"
echo "2. Run workflow with:"
echo "   - Environment: stage"
echo "   - Test type: ios-simulator"
echo ""
echo "3. If successful, try the full CI/CD pipeline:"
echo "   - Go to GitHub Actions → iOS CI/CD Pipeline"
echo "   - Environment: stage"
echo "   - Build type: adhoc"
echo ""

print_header "Security Notes"

print_warning "Important security considerations:"
echo ""
echo "• Never commit .p12 files or .mobileprovision files to git"
echo "• Rotate certificates and provisioning profiles regularly"
echo "• Use app-specific passwords instead of your main Apple ID password"
echo "• Limit API key permissions to minimum required"
echo "• Monitor certificate expiration dates"
echo ""

print_success "Setup preparation complete!"
print_status "Copy the base64 values above to your GitHub secrets"

# Save to files for easy copying
echo "$P12_BASE64" > p12_base64.txt
echo "$PROVISION_BASE64" > provision_base64.txt

print_status "Base64 values also saved to:"
print_status "  - p12_base64.txt"
print_status "  - provision_base64.txt"

print_warning "Remember to delete these files after copying to GitHub!"
echo "rm p12_base64.txt provision_base64.txt"
