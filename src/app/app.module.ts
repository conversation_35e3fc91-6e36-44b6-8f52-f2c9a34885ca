import { VideoCallComponent } from 'src/app/components/video-call/video-call.component';
import { APP_INITIALIZER, NgModule, CUSTOM_ELEMENTS_SCHEMA, Injector } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { Router, RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
// import { StatusBar } from '@ionic-native/status-bar/ngx';
import { AppComponent } from 'src/app/app.component';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { HttpClientModule, HTTP_INTERCEPTORS, HttpClient } from '@angular/common/http';
import { HttpInterceptorService } from 'src/app/services/http-interceptor/http-interceptor.service';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { GraphQLModule } from 'src/app/graphql.module';
import { LoaderComponent } from 'src/app/components/loader/loader.component';
import { CommonMessageComponentModule } from 'src/app/components/common-message/common-message.module';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from 'src/environments/environment';
import { MenuComponentModule } from 'src/app/components/menu/menu.component.module';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import { NgxPermissionsModule } from 'ngx-permissions';
import { Diagnostic } from '@awesome-cordova-plugins/diagnostic/ngx';
import { DocumentPicker } from '@awesome-cordova-plugins/document-picker/ngx';
import { Clipboard } from '@awesome-cordova-plugins/clipboard/ngx';
import { KeychainTouchId } from '@ionic-native/keychain-touch-id/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { SessionService } from 'src/app/services/session-service/session.service';
import { isPresent } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { getValueFromSession, setKeyToSession } from 'src/app/utils/storage-utils';
import { PageRoutes } from 'src/app/constants/page-routes';
import { pageTransition } from 'src/app/ page-transition';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { OKTA_CONFIG, OktaAuthModule } from '@okta/okta-angular';
import { OktaAuth } from '@okta/okta-auth-js';
import { OktaService } from './services/okta/okta.service';
import { Activity } from 'src/app/constants/activity';
import { SharedService } from 'src/app/services/shared-service/shared.service';

/**
 * Initialize Okta token manager when app restarts with existing session
 * This ensures Okta tokens can be refreshed even when login page is bypassed
 * @param injector Injector to access OKTA_CONFIG
 */
async function initializeOktaOnAppRestart(injector: Injector): Promise<void> {
    // alert("DEBUG: initializeOktaOnAppRestart - Function Entry Point (Condition: App restart with existing session)");
  try {
    const oktaLogin = localStorage.getItem(Constants.storageKeys.oktaLogin);
    const oktaTokenStorage = localStorage.getItem(Constants.storageKeys.oktaTokenStorage);

    if (!environment.oktaFlow || !oktaLogin || !oktaTokenStorage) {
     // alert("DEBUG: initializeOktaOnAppRestart - Early Exit (Condition: No oktaFlow, oktaLogin, or oktaTokenStorage)");
      return;
    }

    // Get OktaAuth instance
    const oktaConfig = injector.get(OKTA_CONFIG, null);
    if (!oktaConfig || !oktaConfig.oktaAuth) {
      return;
    }

    const { oktaAuth } = oktaConfig;
    const sharedService = injector.get(SharedService);

    try {
      const currentTokens = oktaAuth.tokenManager.getTokensSync();

      if (!currentTokens.accessToken && oktaTokenStorage) {
       // alert("DEBUG: initializeOktaOnAppRestart - No Current Access Token (Condition: !currentTokens.accessToken && oktaTokenStorage exists)");
        try {
          const storedTokens = JSON.parse(oktaTokenStorage);

          const currentTime = Math.floor(Date.now() / 1000);
          const accessTokenExpiry = storedTokens.accessToken?.expiresAt || 0;
          const timeUntilExpiry = accessTokenExpiry - currentTime;

          if (timeUntilExpiry <= 300) {
           // alert("DEBUG: initializeOktaOnAppRestart - Token Renewal Needed (Condition: timeUntilExpiry <= 300 seconds)");
            try {
              await sharedService.setTokensFollowingOktaRequirements(oktaAuth, storedTokens);
                 //alert("DEBUG: initializeOktaOnAppRestart - Starting Token Renewal (Condition: About to call oktaAuth.token.renewTokens())");
              const renewedTokens = await oktaAuth.token.renewTokens();

              if (renewedTokens && renewedTokens.accessToken && renewedTokens.refreshToken) {
                  // alert("DEBUG: initializeOktaOnAppRestart - Token Renewal Success (Condition: renewedTokens.accessToken && renewedTokens.refreshToken)");
                await sharedService.setTokensFollowingOktaRequirements(oktaAuth, renewedTokens);
              } else {
                throw new Error('Incomplete renewed tokens received from AppModule');
              }
            } catch {
              // Fallback: Set stored tokens.
                // alert("DEBUG: initializeOktaOnAppRestart - Token Renewal Failed, Using Stored Tokens (Condition: Catch block - renewal failed)");
              await sharedService.setTokensFollowingOktaRequirements(oktaAuth, storedTokens);
            }
          } else {
            // Tokens are still valid, set them directly
              // alert("DEBUG: initializeOktaOnAppRestart - Using Valid Stored Tokens (Condition: timeUntilExpiry > 300 seconds)");
            await sharedService.setTokensFollowingOktaRequirements(oktaAuth, storedTokens);
          }
        } catch {
          // Error parsing stored tokens
        }
      } else if (currentTokens.accessToken) {
       // alert("DEBUG: initializeOktaOnAppRestart - Current Access Token Exists (Condition: currentTokens.accessToken exists)");
        const currentTime = Math.floor(Date.now() / 1000);
        const timeUntilExpiry = currentTokens.accessToken.expiresAt - currentTime;

        if (timeUntilExpiry <= 300 && currentTokens.refreshToken) {
          try {
             //  alert("DEBUG: initializeOktaOnAppRestart - Validating and Renewing Current Tokens (Condition: timeUntilExpiry <= 300 && refreshToken exists)");
            await sharedService.validateAndRenewOktaTokens();
            //alert("DEBUG: initializeOktaOnAppRestart - Token Validation Complete (Condition: validateAndRenewOktaTokens() succeeded)");
          } catch {
            // Token renewal failed
          }
        }
      }
    } catch {
      // Error checking current tokens
    }

    // Set up token renewal event listeners
    oktaAuth.tokenManager.on('renewed', (key: string, newToken: any) => {

      if (key === 'accessToken') {
        sharedService.updateNewTokenDetails(newToken);
        localStorage.setItem(Constants.storageKeys.oktaTokenExpiry, newToken.expiresAt.toString());
        // Don't manually store tokens - let Okta handle its own storage
        // Backup storage for our custom logic only
        const allTokens = oktaAuth.tokenManager.getTokensSync();
        localStorage.setItem(Constants.storageKeys.oktaTokenStorage, JSON.stringify(allTokens));
      }
    });

    oktaAuth.tokenManager.on('error', async (err: any) => {
      if (err.name === 'OAuthError' && err.message.includes('The refresh token is invalid or expired')) {
        try {
          const tokens = await oktaAuth.token.renewTokens();
          oktaAuth.tokenManager.setTokens(tokens);
        } catch {
          localStorage.removeItem(Constants.storageKeys.oktaTokenStorage);
          localStorage.removeItem(Constants.storageKeys.oktaTokenExpiry);
          localStorage.removeItem(Constants.storageKeys.oktaLogin);
          sharedService.logout();
        }
      }
    });

    try {
      await oktaAuth.start();
    } catch (serviceError) {
      // eslint-disable-next-line no-console
      console.warn('⚠️ [OKTA] OktaAuth service may already be running:', serviceError);
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ [OKTA] Error initializing Okta on app restart:', error);
  }
}

export function createTranslateLoader(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [AppComponent, LoaderComponent, VideoCallComponent],
  imports: [
    BrowserModule,
    IonicModule.forRoot({
      scrollPadding: false,
      innerHTMLTemplatesEnabled: true,
      scrollAssist: true,
      navAnimation: pageTransition
    }),
    AppRoutingModule,
    HttpClientModule,
    OktaAuthModule,
    CommonMessageComponentModule,
    MenuComponentModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient]
      }
    }),
    GraphQLModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: environment.enableServiceWorker,
      registrationStrategy: 'registerImmediately'
    }),
    NgIdleKeepaliveModule.forRoot(),
    NgxPermissionsModule.forRoot()
  ],
  providers: [
    OktaService, // Add OktaService to providers
    {
      provide: OKTA_CONFIG,
      deps: [OktaService], // Specify OktaService as a dependency
      useFactory: (oktaService: OktaService) => {
        const oktaConfig = oktaService.getOktaConfig(); // Retrieve oktaConfig from OktaService
        const oktaAuth = new OktaAuth(oktaConfig);
        return {
          oktaAuth,
          onAuthRequired: (oktaAuth: OktaAuth, injector: Injector) => {
            const triggerLogin = () => {
              // Redirect the user to your custom login page
              const router = injector.get(Router);
              router.navigate(['/login']);
            };
            if (!oktaAuth.authStateManager.getPreviousAuthState()?.isAuthenticated) {
              // App initialization stage
              triggerLogin();
            } else {
              // Ask the user to trigger the login process during token autoRenew process
            }
          }
        };
      }
    },
    //StatusBar,
    InAppBrowser,
    Diagnostic,
    DocumentPicker,
    Clipboard,
    KeychainTouchId,
    {
      provide: APP_INITIALIZER,
      useFactory: resourceProviderFactory,
      deps: [SessionService, Router, SharedService, Injector],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorService,
      multi: true
    },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    SQLite,
    NativeStorage
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  bootstrap: [AppComponent]
})
export class AppModule {}

/**
 * @description Initialization function to identify route and fetch data using token from local storage before app start.
 * @param sessionService SessionService
 * @param router Router
 * @param sharedService SharedService
 * @returns any
 */
export function resourceProviderFactory(sessionService: SessionService, router: Router, sharedService: SharedService, injector: Injector): any {
  // eslint-disable-next-line no-console
  console.log('🚀 [APP] App initializing');

  sessionService.sessionLoader = true;
  sessionService.setBrandedConfig();
  const authToken = getValueFromSession(Constants.storageKeys.authToken);
  let currentUrl = '/';
  const routeHistory = JSON.parse(sessionStorage.getItem(Constants.storageKeys.routeHistory));
  if (routeHistory && routeHistory.length > 0 && routeHistory[routeHistory.length - 1]) {
    currentUrl = routeHistory[routeHistory.length - 1].url;
  }
  sessionService.applessMessagingFlow = false;
  const currentPathMessage = window.location.href.split(PageRoutes.applessURLPathMessage);
  const currentPathForm = window.location.href.split(PageRoutes.applessURLPathForm);
  const currentPathDoc = window.location.href.split(PageRoutes.applessURLPathDoc);
  const currentPathVideo = window.location.href.split(PageRoutes.applessURLPathVideo);
  const currentPathDownload = window.location.href.split(PageRoutes.applessURLPathDownload);
  const currentPathVisit = window.location.href.split(PageRoutes.applessURLPathVisitView);
  const currentPathUserConsent = window.location.href.split(PageRoutes.appLessUserConsent);
  let applessPath = '';
  if (window.location.href.includes(PageRoutes.applessURLPathMessage)) {
    applessPath = `${PageRoutes.applessURLPathMessage}${currentPathMessage[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathDoc)) {
    applessPath = `${PageRoutes.applessURLPathDoc}${currentPathDoc[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathForm)) {
    applessPath = `${PageRoutes.applessURLPathForm}${currentPathForm[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathVideo)) {
    applessPath = `${PageRoutes.applessURLPathVideo}${currentPathVideo[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathDownload)) {
    applessPath = `${PageRoutes.applessURLPathDownload}${currentPathDownload[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathVisitView)) {
    applessPath = `${PageRoutes.applessURLPathVisitView}${currentPathVisit[1]}`;
  } else if (window.location.href.includes(PageRoutes.appLessUserConsent)) {
    applessPath = `${PageRoutes.appLessUserConsent}${currentPathUserConsent[1]}`;
  } else if (window.location.href.includes(PageRoutes.appLess)) {
    applessPath = `${PageRoutes.appLess}${window.location.href.split(PageRoutes.appLess)[1]}`;
  }
  const authRoutes = [PageRoutes.root, PageRoutes.login, PageRoutes.forgotPassword];
  if (isPresent(applessPath)) {
    return () => {
      // Track activity for app-less link redirection
      sharedService.trackActivity({
        type: Activity.appless,
        name: Activity.pageNavigation,
        des: {
          data: {
            applessPath,
            applessLink: window.location.href
          },
          desConstant: Activity.applessRedirectPathDes
        }
      });
      router.navigateByUrl(applessPath);
    };
  } else if (authToken) {
    if (!navigator.onLine) {
      // Track activity for offline mode redirection
      sharedService.trackActivity({
        name: Activity.pageNavigation,
        des: { desConstant: Activity.redirectToLoginOfflineModeDes }
      });
      return () => {
        sessionService.sessionLoader = false;
        router.navigate(['/home']);
      };
    }
    return () => {
      return sessionService.getSessionData().then(
        (data) => {
          sessionService.sessionLoader = false;
          if (!data || !data.response) {
            if (data.status === 505 || data.status === 401 || authRoutes.includes(currentUrl) === false) {
              // Track activity for no session data redirection
              sharedService.trackActivity({
                name: Activity.pageNavigation,
                des: { desConstant: Activity.redirectToLoginNoSessionDateDes }
              });
              sessionService.resetLocalStorage();
              router.navigate([PageRoutes.login]);
            }
          } else {
            sessionService.userData = data.response;
            sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.falseAsString);
            setKeyToSession({ key: Constants.storageKeys.authToken, value: data.response.authenticationToken });
            initializeOktaOnAppRestart(injector).catch((error) => {
              // eslint-disable-next-line no-console
              console.error('❌ [APP] Error initializing Okta on app restart:', error);
            });

            if (data.response.mySites.length > 1 && data.response.group !== '3' && data.response.config.enable_multisite === '1') {
              let selectedSiteFilter = data.response.mySites;
              if (data.response.defaultSitesFilter) {
                selectedSiteFilter = data.response.defaultSitesFilter;
              }
              sessionService.setDefaultSelectedIds(selectedSiteFilter);
            }
          }
        },
        (err) => {
          sessionService.sessionLoader = false;
          if (authRoutes.includes(currentUrl) === false) {
            sessionService.resetLocalStorage();
            // Track activity for session data error redirection
            sharedService.trackActivity({
              name: Activity.pageNavigation,
              des: { desConstant: Activity.getSessionDataErrorRedirectDes }
            });
            router.navigate([PageRoutes.login]);
          }
        }
      );
    };
  } else {
    return () => {
      sessionService.sessionLoader = false;
      if (authRoutes.includes(currentUrl) === false) {
        sessionService.resetLocalStorage();
        // Track activity for redirection to login
        sharedService.trackActivity({
          name: Activity.pageNavigation,
          des: { desConstant: Activity.redirectToLoginDes }
        });
        router.navigate([PageRoutes.login]);
      }
    };
  }
}
