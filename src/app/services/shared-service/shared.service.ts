import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';
import { Injectable, Renderer2, ViewChild, EventEmitter, Injector } from '@angular/core';
import { Platform, ModalController, ActionSheetController, PopoverController, NavController, IonRouterOutlet } from '@ionic/angular';
import { HttpService } from 'src/app/services/http-service/http.service';
import { map, catchError, delay, retryWhen, take } from 'rxjs/operators';
import { APIs } from 'src/app/constants/apis';
import { Observable, Subject, of, throwError } from 'rxjs';
import {
  AssetSource,
  Constants,
  MessageDeliveryStatus,
  MessagePriority,
  messageTypeAndCategory,
  TagType,
  UserGroup
} from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import {
  isBlank,
  clientToGmtTime,
  uniqueId,
  formatDate,
  isPresent,
  stringToJSON,
  isNumberCheck,
  deepParseJSON,
  getFileReader,
  getMonthRangeData,
  getKeyByValue,
  convertTimeZoneDateTimeToUTCIso,
  deepCopyJSON
} from 'src/app/utils/utils';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { ConfigValues } from 'src/assets/config/config';
import { environment } from 'src/environments/environment';
import { DEFAULT_INTERRUPTSOURCES, Idle } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { Activity } from 'src/app/constants/activity';
import { Urls } from 'src/app/constants/urls';
import { Socket } from 'src/app/constants/socket';
import { CountryPopoverComponent } from 'src/app/components/country-popover/country-popover.component';
import { NgxPermissionsService } from 'ngx-permissions';
import { Config } from 'src/app/constants/config';
import {
  FetchUsersExtraParams,
  MessagethreadOnPolling,
  MessageInboxList,
  MessageInboxPayload,
  MessagesResponse,
  DeliveredUsers
} from 'src/app/interfaces/messages';
import { Organizations } from 'src/assets/organizations/organizations';
import { HttpHeaders, HttpResponse } from '@angular/common/http';
import { ChoosePatientComponent } from 'src/app/pages/message-center/choose-patient/choose-patient.component';
import { ActionPerformed, PushNotificationSchema, PushNotifications, Token, Channel } from '@capacitor/push-notifications';
import { Signature } from 'src/app/constants/signature';
import { LocalNotifications } from '@capacitor/local-notifications';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { PageRoutes } from 'src/app/constants/page-routes';
import { FormIndividualCounts, LoginResponse, UserPushRegistrationSocketData } from 'src/app/interfaces/login';
import { CreateVisitSchemas, ScheduleVisitSchemas, TimeZones } from 'src/app/interfaces/schedule-center';
import { SessionService } from 'src/app/services/session-service/session.service';
import * as moment from 'moment';
import { theme } from 'src/theme/theme';
import {
  BrandConfig,
  Country,
  CountryPopoverCallback,
  FormMessageCountUpdateEvent,
  ExtendedScreenOrientation,
  UserTag,
  GeneralResponse,
  MessageDeliveredUsers,
  AdmissionList,
  Admission
} from 'src/app/interfaces/common-interface';
import { MySignatureRequestCount, DocumentDetailedCountData } from 'src/app/interfaces/document-request-signature';
import { InAppBrowserObject, InAppBrowserOptions } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { InAppBrowserData } from 'src/app/constants/inappbrowser';
import { FileOpener, FileOpenerOptions } from '@capacitor-community/file-opener';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { countryDialCodes } from 'src/app/constants/country-dial-codes';
import { getValueFromLocalStorage, getValueFromSession } from 'src/app/utils/storage-utils';
import { Geolocation } from '@capacitor/geolocation';
import { VideoCall } from 'src/app/constants/video-call';
import { SessionTimeoutComponent } from 'src/app/components/session-timeout/session-timeout.component';
import { Share } from '@capacitor/share';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { AdmissionService } from '../admission-service/admission.service';
import { OKTA_CONFIG } from '@okta/okta-angular';

import { co } from '@fullcalendar/core/internal-common';

declare type FlagApiType = 'thread' | 'msg';
declare type ActionType = 'flag' | 'priority';
declare const msCrypto: any;
export enum PatientType {
  PATIENT = 'patient',
  SINGLE_ASSOCIATED_PATIENT = 'single_associated_patient'
}
/**
 * This service is for sharing functions and data
 * Write functions here to avoid duplicate codes
 */
@Injectable({
  providedIn: 'root'
})
export class SharedService {
  organization: any;
  appTheme: string;
  appLessHomeData = {
    form: [],
    document: [],
    message: [],
  }
  
  // Subscriptions
  appLessHomeNext = new Subject();
  messageListUpdated = new Subject();
  configValuesUpdated = new Subject();
  ssoLoginValueGet = new Subject();
  emojiValueGet = new Subject();
  disableSideMenu = false;
  invokeBackButtonAck = new Subject();
  chatroomUserCountUpdated = new Subject();
  messageFormCountUpdated: Subject<FormMessageCountUpdateEvent> = new Subject();
  documentCountUpdated = new Subject();
  appMinimized = new Subject();
  videoInappResponse = new Subject();
  oktaLogoutRedirect = new Subject();
  chatUsersListUpdated = new Subject();
  documentPollingEvent = new EventEmitter<void>();

  // End Subscriptins
  renderer2: Renderer2;
  vidyoToken: string;
  userData: LoginResponse;
  platformValue: string;
  isLoading = false;
  messageList: MessagesResponse[];
  messageCount: number;
  messageUnreadCount: number;
  formCount: number;
  inviteUserDetails: any;
  inviteUserData: any;
  validationMessage: string;
  rootActivity: number;
  parentActivity: number;
  activityHierarchy: string;
  appConfig;
  localConfig: any;
  routeHistory = [];
  currentPage: string;
  sessionTimeout: number;
  warningTimeout: number;
  magiclinkTokenExpiry: number;
  pushServerAPIUrl: string;
  otherVirtualPatientDetails: any;
  selectedCountry: Country;
  userPermissions: any;
  roomId: string;
  userPage: string;
  fromEdit = false;
  timezone = environment.timezone;
  signatureStatus: boolean;
  notificationSound: string;
  docDetails: any = {};
  @ViewChild(IonRouterOutlet, { static: true }) routerOutlet: IonRouterOutlet;
  platforms: string;
  setRouterLink: string;
  externalIntegration: any;
  selectedAssociatePatient: any;
  automaticLinkedItems: any;
  consoloUserData: any;
  consoloLoader = false;
  disableSiteFilter = false;
  tempPatientSiteId: any = [0];
  patientDataList: any;
  patientSearchSiteIds: any = [];
  roomID: number;
  storeSelectedSite: any = [];
  selectedTag: any = [];
  virtualPatientdata: any = {};
  patientNewInfo: any = {};
  applessDocumentId: number;
  timeZones: TimeZones;
  hideMessageCount = true;
  reloadForNewChatroomPollling = 0;
  formIndividualCounts: FormIndividualCounts;
  editAlternateContact = false;
  brandConfig: BrandConfig;
  inviteFromAdd = false;
  isSwipeGestureEnabled = true;
  documentCount: MySignatureRequestCount;
  loaderMessage = '';
  browser: InAppBrowserObject;
  isVideoButtonCleared: boolean;
  videoButtonInterval: ReturnType<typeof setInterval>;
  socketClientId: string;
  inAppBrowserData: InAppBrowserData;
  userLastActivityTimeStamp: number;
  sessionModal: boolean;
  isAppMinimize: boolean;
  // TODO: Polling need to confirm
  // messagePollingSelfUpdate: EventEmitter<any> = new EventEmitter<any>();
  // take from env/constants
  vcLatitude;
  vcLongitude;
  isPermissionGranted = false;
  sessionTimedOutInInappBrowser: boolean;
  isIOSVideoCallConnected = false;
  videoCall = false;
  offlineLogin = false;
  selectedDateOptions;
  selectedDateRange;
  branchSwitched = false;
  patientLabel: string;
  caregiverLabel: string;
  $messageDeleteRestore = new Subject();
  private notificationListenerAdded = false;
  constructor(
    private admissionService: AdmissionService,
    public platform: Platform,
    private httpService: HttpService,
    private readonly route: ActivatedRoute,
    private readonly common: CommonService,
    private readonly modalController: ModalController,
    public readonly socketService: SocketService,
    private readonly ngxPermissionsService: NgxPermissionsService,
    private readonly actionSheetController: ActionSheetController,
    public readonly popoverController: PopoverController,
    private readonly navCtrl: NavController,
    public idle: Idle,
    public keepalive: Keepalive,
    private router: Router,
    public sessionService: SessionService,
    private injector: Injector
  ) {
    const localDateRangeFilterFormsDateOptions = localStorage.getItem(Constants.storageKeys.dateRangeFilterFormsDateOptions);
    this.selectedDateOptions = localDateRangeFilterFormsDateOptions
      ? Number(localDateRangeFilterFormsDateOptions)
      : Constants.filterSelectedOptions.lastMonth;

    this.selectedDateRange =
      this.selectedDateOptions === Constants.filterSelectedOptions.custom
        ? JSON.parse(localStorage.getItem(Constants.storageKeys.dateRangeFilterForms))
        : JSON.parse(JSON.stringify(Constants.resetSelectedDateRange));
    const platformInfo = platform.platforms();
    this.platforms = platformInfo.join('-');
    this.userData = this.sessionService.userData;
    this.platformValue = this.platform.is('ios') ? 'ios' : this.platform.is('android') ? 'android' : 'web';
    this.httpService.paramsObj.platform = this.platformValue;
    this.setTheme();
    this.patientLabel = this.common.getTranslateData('GENERAL.PATIENT');
    this.caregiverLabel = this.common.getTranslateData('GENERAL.CAREGIVER');
  }
  setTheme(): void {
    if (isBlank(this.organization)) {
      const organization = localStorage.getItem(Constants.storageKeys.organization);
      this.organization = isBlank(organization) ? Organizations.config.default : Organizations.config[organization];
    }
    this.appTheme = this.organization.theme;
    if (isBlank(this.brandConfig)) {
      const brandConfig = localStorage.getItem(Constants.storageKeys.brandConfig);
      if (isPresent(JSON.parse(brandConfig))) {
        this.brandConfig = JSON.parse(brandConfig);
      }
    }
  }
  isUserLoggedIn(): boolean {
    if (!isBlank(getValueFromSession(Constants.storageKeys.authToken))) {
      return true;
    }

    return false;
  }
  fetchImageViewerModalProps(item, index) {
    const imageElement = Constants.allowedImageFileTypes.includes(item.name.split('.').pop());
    const visitScheduleDownloadFileFormats = VisitScheduleConstants.visitScheduleDownloadFileFormats.includes(
      item.name.split('.').pop()
    );
    let url, type;
    url = `${environment.visitScheduleAttachmentUrl}${item.file_path}${item.name}`;
    const downloadUrl = url;
    if (!visitScheduleDownloadFileFormats) {
      if (imageElement) {
        type = Constants.documentTypes.image;
      } else {
        type = Constants.documentTypes.pdf;
      }
      return { url, downloadUrl, type };
    } else {
      const doc = document.getElementById(`attach-uploaded-file-link-${index}`) as HTMLAnchorElement;
      doc.href = downloadUrl;
    }
  }

  presentPdfFromLink(params: { url: string; source?: string; isDownloadDoc?: boolean }) {
    return new Promise((resolve) => {
      const { url, source, isDownloadDoc } = params;
      this.httpService.doFetchFileBuffer({ endpoint: url, isExternalLink: true }).subscribe(
        (response: HttpResponse<ArrayBuffer>) => {
          const contentTypeHeader = response.headers.get('content-type');
          const fileNameCache =
            source === AssetSource.CMIS
              ? `${url.split('.json')[0]?.split('/')?.pop()}${Constants.documentTypes.pdf}`
              : url.split('/').pop().substring(Constants.uploadedVisitFileNameDefaultSubstringLength);
          let fileName = '';
          if (source === AssetSource.SCHEDULE_CENTER) {
            fileName = fileNameCache;
          } else {
            fileName = contentTypeHeader
              ? contentTypeHeader
                ?.split(';')
                ?.find((x) => x.includes('name='))
                ?.split('name=')[1]
              : fileNameCache;
          }
          const blob = new Blob([new Uint8Array(response.body)], { type: Constants.extToMimes.pdf });
          resolve(this.fetchFileFromBlob({ blob, fileName, isDownloadDoc }));
        },
        () => {
          if (this.platform.is('capacitor')) {
            resolve({ status: false });
          } else {
            resolve({ status: true, url });
          }
        }
      );
    });
  }
  inAppBrowserOptions(): InAppBrowserOptions {
    return {
      toolbarposition: 'top',
      location: 'no',
      closebuttoncolor: Constants.whiteColor,
      toolbarcolor: theme.statusBarColor
    };
  }
  fetchFileFromBlob(params: { blob: Blob; fileName: string; isDownloadDoc?: boolean }) {
    return new Promise((resolve) => {
      const { blob, fileName, isDownloadDoc } = params;
      // Download file for web
      if (isDownloadDoc && !this.platform.is('capacitor')) {
        const fileUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = fileUrl;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        a.remove();
        resolve({ status: false });
      } else {
        const reader = getFileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = async () => {
          const base64Data = <string>reader.result;
          if ((this.platform.is('android') || isDownloadDoc) && this.platform.is('capacitor')) {
            try {
              let directory = Directory.Cache;

              if (isDownloadDoc) {
                directory = Directory.ExternalStorage;
              }
              await Filesystem.requestPermissions();
              const finalPhotoUri = await Filesystem.writeFile({
                path: `Download/${fileName}`,
                data: base64Data.split(',')[1],
                directory,
                recursive: true
              });
              if (finalPhotoUri.uri === '') {
                resolve({ status: false });
              }
              if (finalPhotoUri.uri !== '') {
                this.isLoading = false;
                // Download file for device
                if (isDownloadDoc) {
                  if (this.platform.is('ios')) {
                    resolve(this.shareDocument(finalPhotoUri.uri));
                  } else {
                    const message = this.common.getTranslateData('MESSAGES.FILE_DOWNLOAD_MESSAGE');
                    this.common.showToast({ message, duration: 4000 });
                  }
                } else {
                  resolve(this.openFile({ url: finalPhotoUri.uri }));
                }
              }
            } catch (e) {
              resolve({ status: false });
            }
          } else if (this.platform.is('ios') && this.platform.is('capacitor')) {
            resolve({ url: base64Data, status: true });
          } else {
            resolve({ status: true, url: URL.createObjectURL(blob) });
          }
        };
      }
    });
  }
  openFile(params: { url: string }) {
    return new Promise((resolve) => {
      const options: FileOpenerOptions = {
        filePath: params.url
      };
      FileOpener.open(options)
        .then(() => {
          this.isLoading = false;
        })
        .catch(() => {
          resolve({ status: false });
        });
    });
  }

  webPushNotificationPermission(): void {
    if (!('Notification' in window)) {
      console.log('This browser does not support desktop notification');
    } else if (Notification.permission === 'granted') {
      this.isPermissionGranted = true;
      this.enablePushNotificationSocket();
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          this.isPermissionGranted = true;
          this.enablePushNotificationSocket();
        }
      });
    }
  }
  // TODO: CHP-3664
  // TODO: CHP-3664
  async checkPermission() {
    // Request permission to use push notifications
    // iOS will prompt user and return if they granted permission or not
    // Android will just grant without prompting for version below 12, for 13+,checkPermissions added.
    if (this.platform.is('ios')) {
      this.requestPermission();
      return;
    }
    PushNotifications.checkPermissions().then((permission) => {
      if (permission.receive === 'prompt' || permission.receive === 'prompt-with-rationale') {
        this.requestPermission();
      } else if (permission.receive === 'granted') {
        this.registerPush();
      }
    });
  }
  requestPermission() {
    PushNotifications.requestPermissions().then((result) => {
      if (result.receive === 'granted') {
        this.registerPush();
      } else {
        // Show some error
      }
    });
  }
  registerPush(): void {
    this.isPermissionGranted = true;
    // Register with Apple / Google to receive push via APNS/FCM
    PushNotifications.register();
    this.trackActivity({
      type: Activity.notifications,
      name: Activity.notificationsGranted,
      des: {
        data: { appName: theme.name },
        desConstant: Activity.pushNotificationPermissionDes
      }
    });
  }
  emitUserPushRegistrationEvent(userPushRegistrationData: any, retryEmitUserPushRegistration = false): void {
    const timeoutDuration = 3000;
    let timeoutId: ReturnType<typeof setTimeout>;
    timeoutId = setTimeout(() => {
      const emitUserPushRegistrationAlertData = {
        alertId: 'push-reg-soc-failed',
        message: this.common.getTranslateData('CUSTOM_ERROR.PUSH_REG_FAILED'),
        buttons: [
          {
            text: this.common.getTranslateData('BUTTONS.CANCEL'),
            confirm: false
          },
          {
            text: this.common.getTranslateData('BUTTONS.RETRY'),
            confirm: true
          }
        ]
      };
      retryEmitUserPushRegistration = true;
      this.common.showAlert(emitUserPushRegistrationAlertData).then((confirmation) => {
        if (confirmation) {
          clearTimeout(timeoutId);
          this.emitUserPushRegistrationEvent(userPushRegistrationData, retryEmitUserPushRegistration);
        }
      });
    }, timeoutDuration);
    if (userPushRegistrationData && this.socketService.status) {
      this.socketService.emitEvent(Socket.userPushDeviceRegistration, userPushRegistrationData, (status: boolean) => {
        if (status) {
          localStorage.setItem(Constants.storageKeys.userPushDeviceRegistration, Constants.success);
          clearTimeout(timeoutId);
          if (retryEmitUserPushRegistration) {
            this.common.showToast({ message: this.common.getTranslateData('SUCCESS_MESSAGES.PUSH_REGISTER_SUCCESS') });
          }
        }
      });
    }
  }

  userPushDeviceRegistration(payload: any, retryRegistration = false): void {
    this.httpService
      .doPostByUrl({ payload, apiUrl: `${environment.pushServerAPIUrl}${Urls.createAWSPlatformUserEndpoint}` }, true)
      .pipe(
        retryWhen((errors) => errors.pipe(delay(2000), take(3))),
        catchError((error) => {
          console.error(`API request failed: ${error}`);
          return throwError(error);
        })
      )
      .subscribe(
        (data) => {
          const userPushRegistrationData: UserPushRegistrationSocketData = {
            uniqueDeviceId: localStorage.getItem(Constants.storageKeys.deviceID),
            registrationId: localStorage.pushNotificationToken,
            userId: localStorage.userId,
            platform: this.platformValue
          };
          localStorage.setItem(
            Constants.storageKeys.userPushDeviceRegistration,
            JSON.stringify(userPushRegistrationData)
          );
          if (data?.status === Constants.success.toLowerCase()) {
            if (!('pushRegComplete' in data)) {
              this.emitUserPushRegistrationEvent(userPushRegistrationData, retryRegistration);
            } else {
              this.common.showToast({ message: this.common.getTranslateData('SUCCESS_MESSAGES.PUSH_REGISTER_SUCCESS') });
            }
          }

          this.trackActivity({
            type: Activity.notifications,
            name: Activity.userPushDeviceRegistration,
            des: {
              data: { registrationData: userPushRegistrationData, appName: theme.name },
              desConstant: Activity.userPushDeviceRegistrationDes
            }
          });

        },
        (error) => {
          const createAWSPlatformUserEndpointAlertData = {
            alertId: 'push-reg-aws-failed',
            message: this.common.getTranslateData('CUSTOM_ERROR.PUSH_REG_FAILED'),
            buttons: [
              {
                text: this.common.getTranslateData('BUTTONS.CANCEL'),
                confirm: false
              },
              {
                text: this.common.getTranslateData('BUTTONS.RETRY'),
                confirm: true
              }
            ]
          };
          retryRegistration = true;
          this.common.showAlert(createAWSPlatformUserEndpointAlertData).then((confirmation) => {
            if (confirmation) {
              this.userPushDeviceRegistration(payload, retryRegistration);
            }
          });
        }
      );
  }

  nativePushNotificationEnable(): void {
    // TODO: CHP-3664
    PushNotifications.removeAllListeners();
    this.checkPermission();
    if (this.platform.is('android')) {
      PushNotifications.listChannels().then(({ channels }) => {
        channels.forEach((channel) => {
          if (channel.id === Constants.pushDefaultForeground) {
            PushNotifications.deleteChannel({ id: channel.id });
          }
        });
      });
      this.createChannelsFromSoundList();
    }
    PushNotifications.addListener('registration', (token: Token) => {
      localStorage.pushNotificationToken = token.value;
      localStorage.userId = this.userData.userId;

      const payload = {
        userId: this.userData.userId,
        registrationId: token.value,
        osType: this.platformValue,
        bundleIdentifier: theme.bundleIdentifier,
        uniqueDeviceId: localStorage.getItem(Constants.storageKeys.deviceID)
      };
      this.userPushDeviceRegistration(payload);
    });
    this.pushNotificationActions();
  }

  createChannelsFromSoundList() {
    Constants.notificationSoundList.forEach((sound) => {
      const channel: Channel = {
        id: `${sound.name.toLowerCase()}`,
        description: `Citus Notification ${sound.name}`,
        visibility: 1,
        name: `Citus Notification ${sound.name}`,
        importance: 5,
        sound: sound.name.toLowerCase()
      };

      PushNotifications.listChannels().then(({ channels }) => {
        if (!channels.some((existingChannel) => existingChannel.id === channel.id)) {
          PushNotifications.createChannel(channel);
        }
      });
    });
  }

  pushNotificationActions() {
    // Some issue with our setup and push will not work
    PushNotifications.addListener('registrationError', (error: any) => {
      // do nothing
    });

    PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
      // TODO: Handle foreground push notification
    });

    // Method called when tapping on a notification
    PushNotifications.addListener('pushNotificationActionPerformed', (notification: ActionPerformed) => {
      // TODO: Handle Push received action
      this.handlePushNotificationAction(notification);
    });

    this.trackActivity({
      type: Activity.notifications,
      name: Activity.pushNotificationEvents,
      des: {
        data: { device: this.platform, appName: theme.name, uniqueDeviceId: localStorage.getItem(Constants.storageKeys.deviceID), token: localStorage.pushNotificationToken },
        desConstant: Activity.pushNotificationEventsDes
      }
    });
  }
  /**
   * To handle push notification action
   * @param notification ActionPerformed
   * @returns void
   */
  handlePushNotificationAction(notification: ActionPerformed): void {
    const { data } = notification.notification;
    let userData = isPresent(data.aps) ? data.aps['user-data'] : data['user-data'];
    userData = typeof userData === 'object' ? JSON.stringify(userData) : userData;
    const chatroomId = userData?.pushDeepLink?.activeMessage?.chatroomid;
    if (!isBlank(this.roomID)) {
      this.updateChatRoomId(Number(this.roomID), chatroomId,true);
    }
    this.addRouteLink(userData);
  }
  addRouteLink(data: any): void {
    const jsonData = stringToJSON(data);
    if (jsonData && jsonData?.pushDeepLink) {
      this.deepLinkRedirect(jsonData.pushDeepLink);
    }
  }

  deepLinkRedirect(deeplink): void {
    let pageRoute = '';
    const { stateParams, state } = deeplink;
    let navigationExtras: NavigationExtras = {};
    if (state === Constants.deepLinkingStates.groupChat) {
      pageRoute = PageRoutes.activeMessages;
      if (isPresent(stateParams?.targetID)) {
        pageRoute = `${PageRoutes.chatRoom}/${stateParams?.targetID}`;
      }
      navigationExtras = { replaceUrl: true };
    } else if (state === Constants.deepLinkingStates.formsCenter) {
      pageRoute = PageRoutes.pendingForms;
    } else if (state === Constants.deepLinkingStates.documentCenter) {
      pageRoute = PageRoutes.documentCenter;
    } else if (state === Constants.deepLinkingStates.deliveryCenter) {
      pageRoute = PageRoutes.deliveryCenter;
      if (stateParams && stateParams?.Ticketnumber && stateParams?.uid) {
        pageRoute = `${PageRoutes.deliveryDetails}/${stateParams.Ticketnumber}/${stateParams.uid}/`;
      }
    } else if (state === Constants.deepLinkingStates.scheduleCenter) {
      pageRoute = PageRoutes.scheduleCenterVisits;
      if (isPresent(stateParams?.targetID)) {
        navigationExtras = {
          state: {
            data: {
              visitKey: stateParams.targetID,
              visitCalendarData: stateParams?.targetDetails,
              disableCalendarView: true
            }
          }
        };
        pageRoute = `${PageRoutes.scheduleCenterVisitsView}/${stateParams.targetID}/${moment().unix()}`;
      }
    }
    if (isPresent(pageRoute)) {
      if (this.isUserLoggedIn()) {
        this.router.navigate([pageRoute], {
          queryParams: { timestamp: new Date().getTime() },
          replaceUrl: true,
          state: navigationExtras.state // Preserve state if needed
        });
      } else {
        this.setRouterLink = pageRoute;
      }
    }
  }
  confirmReviewPushNotification(message: any) {
    const { chatroomid, chatroom_id, sent, userid } = message;
    const targetID = chatroomid || chatroom_id;

    this.socketService.emitEvent(Socket.confirmReviewPushNotification, { chatRoomId: targetID, messageUserId: userid }, (data: any, type: any) => {
      if (type && data < sent) {
        const deepLinking = {
          state: Constants.deepLinkingStates.groupChat,
          stateParams: {
            targetID,
            targetName: Constants.pushNotificationGroupChatTarget
          },
          activeMessage: {
            sent,
            messageType: message.messageType || 0,
            baseId: message.baseId || 0,
            userid,
            fromName: `"${message.fromName}"`,
            message_group_id: message.message_group_id || 0,
            createdby: message.createdby || 0
          }
        };
        const notificationData = {
          sourceId: Constants.sourceId.message.toString(),
          sourceCategoryId: Constants.sourceCategoryId.staffReviewingPatientMessage
        };
        this.sentPushNotification(userid, '0', ConfigValues.messages.yourMessageIsGettingReviewed, '', deepLinking, '', notificationData);
      }
    });
  }

  async pushLocalNotification(notification: PushNotificationSchema): Promise<void> {
    const channel = this.platform.is('android')
      ? { channelId: `${notification.data?.soundname}`, smallIcon: 'ic_notifications_small' }
      : { largeIcon: 'ic_notifications' };
    const scheduleId = isNumberCheck(notification?.id) ? Number(notification.id) : Math.floor(Math.random() * 100);
    const notificationSound = `${notification.data.soundname}${this.platform.is('ios') ? '.caf' : ''}`;
    await LocalNotifications.schedule({
      notifications: [
        {
          title: theme.name,
          body: isPresent(notification.body) ? notification.body : notification.data?.message,
          id: scheduleId,
          sound: notificationSound,
          attachments: null,
          actionTypeId: '',
          extra: notification.data,
          ...channel
        }
      ]
    });
    this.addNotificationListener();
  }

  async addNotificationListener() {
    if (this.notificationListenerAdded) {
      return; // Skip if already added
    }
    this.notificationListenerAdded = true;

    await LocalNotifications.addListener('localNotificationActionPerformed', (eventData: any) => {
      let userData = eventData.notification.extra['user-data'];
        userData = typeof userData === 'object' ? JSON.stringify(userData) : userData;
        const parsedData = typeof userData === 'object' ? userData : JSON.parse(userData);
      const chatroomId = parsedData.pushDeepLink.activeMessage.chatroomid;
      if (!isBlank(this.roomID)) {
        this.updateChatRoomId(Number(this.roomID), chatroomId, true);
      }
      this.addRouteLink(userData);
    });
  }

  isEnableConfig(name: string): boolean {
    const value = this.userData?.config[name];
    return String(value) === Constants.configTrue;
  }
  isEnableSiteConfig(name: string): boolean {
    const value = this.userData?.siteConfigs[name];
    return String(value) === Constants.configTrue;
  }

  isConfigTrue(value: string): boolean {
    return String(value) === Constants.configTrue;
  }

  getConfigValue(name: string): string {
    const value = this.userData?.config[name] || '';
    return String(value);
  }

  getSiteConfigValue(name: string): string {
    const value = this.userData?.siteConfigs[name] || '';
    return String(value);
  }

  getSiteTenantConfigValue(name: string): string {
    const siteConfigValue = this.getSiteConfigValue(name);
    if (!isBlank(siteConfigValue)) {
      return String(siteConfigValue);
    }
    const tenantConfigValue = this.getConfigValue(name);
    return String(tenantConfigValue);
  }

  isAppLessHomeLoggedIn(): boolean {
    const isAppLessHomeLoggedIn = sessionStorage.getItem(Constants.storageKeys.appLessHomeLoggedIn);
    if (isBlank(isAppLessHomeLoggedIn)) {
      return false;
    }
    return isAppLessHomeLoggedIn === Constants.trueAsString;
  }
  /**
   * executeSaveAsDraft
   * @param retry number No of retrys before force logout
   */
  executeSaveAsDraft(retry = Constants.saveAsDraftMaxRetry) {
    this.browser?.executeScript({ code: this.inAppBrowserData.triggerSaveAsDraft });
    if (retry > -1) {
      this.trackActivity({
        type: Activity.forms,
        name: Activity.formSaveAsDraftOnSessionExpiry,
        des: {
          data: { userEmail: this.userData?.userName, retry: `(${Constants.saveAsDraftMaxRetry + 1 - retry})` },
          desConstant: Activity.formSaveAsDraftOnSessionExpiryDes
        }
      });
      this.browser?.executeScript({ code: this.inAppBrowserData.setSaveAsDraftOnSessionTimeOut });
      const message = this.common.getTranslateDataWithParam('MESSAGES.SAVE_AS_DRAFT_SESSION_TIMEOUT', { retry });
      this.inAppBrowserExecuteAddOnScript({ message, from: Constants.inAppAddOnFromPath.message });
      this.sessionTimedOutInInappBrowser = true;
    }
  }
  /**
   * closeInAppOnSessionTimeout
   * @param isSaveAsDraftSuccess boolean save as draft success status
   * @param type string From which service, browser is triggered
   */
  closeInAppOnSessionTimeout(isSaveAsDraftSuccess = false, type = Constants.forms): void {
    this.sessionTimedOutInInappBrowser = false;
    if (type === Constants.forms) {
      this.trackActivity({
        type: Activity.userAccess,
        name: Activity.sessionTimeout,
        des: {
          data: { userEmail: this.userData?.userName },
          desConstant: isSaveAsDraftSuccess
            ? Activity.sessionTimeoutFormDraftSuccessDes
            : Activity.sessionTimeoutFormDraftFailedDes
        }
      });
    }
    this.logout();
    if (type === Constants.forms) {
      const message = this.common.getTranslateData(
        isSaveAsDraftSuccess
          ? 'MESSAGES.SAVE_AS_DRAFT_SESSION_TIMEOUT_SUCCESS'
          : 'MESSAGES.SAVE_AS_DRAFT_SESSION_TIMEOUT_FAILED'
      );
      this.common.showMessage(message);
    }
  }
  closeInAppSession() {
    if (this.browser) {
      this.browser.close();
    }
    this.browser = undefined;
  }

  updateNewTokenDetails(newToken): void {
    const previousTokenExpiry = localStorage.getItem(Constants.storageKeys.oktaTokenExpiry);
    if( previousTokenExpiry !== newToken.expiresAt.toString()) {
      localStorage.setItem(Constants.storageKeys.oktaTokenExpiry, newToken.expiresAt.toString());
      this.httpService
      .doPost({
        hasOktaAuthToken: true,
        endpoint: APIs.manageIdmToken,
        payload: {
          action: 'update'
        },
        contentType: 'json',
        headers: { idmToken: newToken.accessToken },
        parseToString: false,
        version: Constants.apiVersions.apiV4,
        loader: false
      })
      .subscribe({
        next: ({ data, success }: { data: any; success: boolean, status: Object }) => {
        },
        error: () => {
        }
      });
     }
  }

  /**
   * Sets tokens following Okta's requirements - let Okta manage its own storage
   * @param oktaAuth OktaAuth instance
   * @param tokens Tokens to set
   * @returns Promise<boolean> indicating success
   */
  async setTokensFollowingOktaRequirements(oktaAuth: any, tokens: any): Promise<boolean> {
    try {
      // Validate we have both required tokens
      if (!tokens.accessToken || !tokens.refreshToken) {
        return false;
      }

      try {
        await oktaAuth.stop();
      } catch (stopError) {
        console.warn('⚠️ [OKTA] OktaAuth service may not have been running:', stopError);
      }
      oktaAuth.tokenManager.clear();
      oktaAuth.tokenManager.setTokens(tokens);
      await oktaAuth.start();
      localStorage.setItem(Constants.storageKeys.oktaTokenExpiry, tokens.accessToken.expiresAt.toString());
      this.updateNewTokenDetails(tokens.accessToken);
      return true;
    } catch (error) {
      console.error('❌ [OKTA] Error setting tokens in shared service:', error);
      return false;
    }
  }

  /**
   * Validates and renews Okta tokens following Okta's requirements
   * @param forceRenewal - Force renewal regardless of expiry status
   * @returns Promise<boolean> indicating if tokens are valid/renewed successfully
   */
  async validateAndRenewOktaTokens(forceRenewal: boolean = false): Promise<boolean> {
    try {
      const oktaConfig = this.injector.get(OKTA_CONFIG, null);
      if (!oktaConfig || !oktaConfig.oktaAuth) {
        return false;
      }

      const { oktaAuth } = oktaConfig;
      const currentTokens = oktaAuth.tokenManager.getTokensSync();
      if (!currentTokens.accessToken || !currentTokens.refreshToken) {
        return false;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = currentTokens.accessToken.expiresAt - currentTime;
      const needsRenewal = forceRenewal || timeUntilExpiry <= 300; // Less than 5 minutes

      if (!needsRenewal) {
        return true;
      }

      if (!oktaAuth.tokenManager.isStarted()) {
        oktaAuth.tokenManager.start();
      }

      const renewedTokens = await oktaAuth.token.renewTokens();
      if (renewedTokens && renewedTokens.accessToken && renewedTokens.refreshToken) {
        // Use consolidated token setting function
        const success = await this.setTokensFollowingOktaRequirements(oktaAuth, renewedTokens);
        if (success) {
          return true;
        }
        return false;
      }
      return false;
    } catch (error) {
      console.error('❌ [OKTA] Error in token validation/renewal in shared service:', error);
      return false;
    }
  }

  logout(rediectToLogin = true): void {
    this.closeInAppSession();
    this.common.dismissModal();
    const tenantId = this.userData?.tenantId;
    this.socketService.emitEvent(Socket.forseDisconnect, { tenantId });
    this.setRouterLink = '';
    const description = {
      userName: this.userData?.userName || this.userData?.username,
      currentPage: this.currentPage
    };
    const activityData = {
      type: Activity.userAccess,
      name: Activity.userLogout,
      des: { data: description, desConstant: Activity.logout }
    };
    this.httpService
      .doPost({
        endpoint: APIs.logout,
        payload: {}
      })
      .subscribe(
        () => {
          // do nothing
        },
        (error) => {
          this.errorHandler(error);
        }
      );
    this.common.closeAllAlert('push-reg-soc-failed');
    this.common.closeAllAlert('push-reg-aws-failed');
    this.trackActivity(activityData);
    const oktaTokenStorage = JSON.parse(localStorage.getItem(Constants.storageKeys.oktaTokenStorage));
    this.sessionService.resetLocalStorage();
    this.routeHistory = [];
    this.currentPage = '';
    this.resetSessionTimeout(false);
    this.ngxPermissionsService.flushPermissions();
    this.messageCount = undefined;
    this.messageList = undefined;
    this.offlineLogin = false;
    this.socketService.connectSocket();
    this.loaderMessage = '';
    if (rediectToLogin) {
      this.isSwipeGestureEnabled = true;
      if (environment.oktaFlow && !isBlank(oktaTokenStorage)) {
        // this.oktaAuth.signOut();
        this.oktaLogoutRedirect.next(null);
      } else {
        this.navCtrl.navigateRoot('login');
      }
    }
    this.userData = undefined;
    this.clearAllDeliveredNotifications();
  }
  get userSiteIds(): number[] {
    return isPresent(this.userData.mySites) ? this.userData.mySites?.map((element) => Number(element?.id)) : [];
  }
  getEsiFromExternalSystem(externalSystem): string {
    const { guId, filenameExpression, externalAdmissionId } = externalSystem;
    let esi = '';
    if (this.isMultiAdmissionsEnabled && isBlank(externalAdmissionId)) return esi;
    if (isPresent(guId) && isPresent(filenameExpression)) {
      esi = guId === filenameExpression ? filenameExpression : `${guId}(${filenameExpression})`;
    } else if (guId || filenameExpression) {
      esi = isPresent(guId) ? guId : filenameExpression;
    }
    return esi;
  }
  /**
   * Gets the selected site IDs.
   * If multi-site is enabled, it retrieves the selected sites from local storage.
   * Otherwise, it returns the user's site IDs.
   * @returns An array of selected site IDs.
   */
  get selectedSites(): number[] {
    const siteIds = [];
    if (this.isEnableConfig(Config.enableMultiSite)) {
      const selectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
      if (isPresent(selectedSites)) {
        siteIds.push(...JSON.parse(selectedSites));
      }
    }
    if (isBlank(siteIds)) {
      siteIds.push(...this.userSiteIds);
    }
    return siteIds;
  }
  getFilterDateRange(
    selectedDateOptions: number,
    selectedDateRange?: { from: string; to: string },
    format?
  ): {
    startDate: string;
    endDate: string;
  } {
    const dateFilterParams = { startDate: '', endDate: '' };
    const selectedMonth = [
      Constants.filterSelectedOptions.lastMonth,
      Constants.filterSelectedOptions.lastThreeMonth,
      Constants.filterSelectedOptions.lastSixMonth
    ];
    if (selectedMonth.includes(selectedDateOptions) || isBlank(selectedDateOptions)) {
      const key = getKeyByValue(
        Constants.filterSelectedOptions,
        isBlank(selectedDateOptions) ? Constants.filterSelectedOptions.lastMonth : selectedDateOptions
      );
      const getMonthData = getMonthRangeData(Constants.dateFormat.ymd, Constants.monthRange[key], Constants.monthRangeType[key]);
      if (format) {
        dateFilterParams.startDate = getMonthData.previousMonthDate;
        dateFilterParams.endDate = getMonthData.todaysDate;
      } else {
        dateFilterParams.startDate = convertTimeZoneDateTimeToUTCIso(
          getMonthData.previousMonthDate,
          Constants.startOfDay,
          moment.tz.guess(),
          '',
          format
        );
        dateFilterParams.endDate = convertTimeZoneDateTimeToUTCIso(getMonthData.todaysDate, Constants.endOfDay, moment.tz.guess(), '', format);
      }
    }
    if (selectedDateOptions === Constants.filterSelectedOptions.custom && isPresent(selectedDateRange?.from) && isPresent(selectedDateRange?.to)) {
      const formatStartDate = formatDate(selectedDateRange.from, Constants.dateFormat.ymd);
      const formatEndDate = formatDate(selectedDateRange.to, Constants.dateFormat.ymd);
      if (format) {
        dateFilterParams.startDate = formatStartDate;
        dateFilterParams.endDate = formatEndDate;
      } else {
        dateFilterParams.startDate = convertTimeZoneDateTimeToUTCIso(formatStartDate, Constants.startOfDay, moment.tz.guess(), '', format);
        dateFilterParams.endDate = convertTimeZoneDateTimeToUTCIso(formatEndDate, Constants.endOfDay, moment.tz.guess(), '', format);
      }
    }
    return dateFilterParams;
  }
  /**
   * Fetches all messages based on the provided request body.
   *
   * @param reqBody - The request body containing the filter options for fetching messages.
   * @returns An Observable that emits an object containing the fetched messages.
   */
  fetchAllMessages(reqBody?: MessageInboxPayload): Observable<{ message: MessagesResponse[]; totalUnreadMessagesCount?: number }> {
    const url = APIs.fetchMessages;
    const dateRange = this.getFilterDateRange(reqBody?.selectedDateOptions, reqBody?.dateRange);
    const requestBody: {
      archived: boolean;
      page: number;
      filter: {
        tagIds: number[];
        flagId: number;
        priority: number;
        mention: boolean;
        unread: boolean;
        dateRange: { start: string; end: string };
        searchKeyword: string;
        siteIds?: number[];
        chatThreadTypes?: number[]
      };
    } = {
      archived: reqBody?.archived ?? false,
      page: (reqBody?.pageCount ?? 0) + 1,
      filter: {
        tagIds: reqBody?.filterTags ?? [],
        flagId: reqBody?.flagValue ?? 0,
        priority: reqBody?.priorityValue ?? 0,
        mention: reqBody?.mentionUsers ?? false,
        unread: reqBody?.unread ?? false,
        dateRange: { start: dateRange.startDate, end: dateRange.endDate },
        searchKeyword: reqBody?.searchKeyword ?? '',
        siteIds: isPresent(reqBody?.siteIds) ? reqBody.siteIds : this.selectedSites,
        chatThreadTypes: reqBody?.chatThreadTypes ?? []
      }
    };
    return this.httpService
      .doPost({
        endpoint: url,
        payload: requestBody,
        contentType: 'json',
        parseToString: false,
        version: Constants.apiVersions.apiV5,
        loader: false
      })
      .pipe(
        map((x: GeneralResponse<MessageInboxList>) => ({
          message: x?.data?.messages || [],
          totalUnreadMessagesCount: x?.data?.totalUnreadMessagesCount || 0
        })),
        catchError(() => of({ message: [], totalUnreadMessagesCount: 0 }))
      );
  }
  enableMessageSocket(): void {
    // TODO enableMessageSocket called twice, need to remove unnecessary usage
    const chatroom = location.pathname.split('/message-center/messages/active/chat/');
    this.socketService.subscribeEvent(Socket.messagePolling).subscribe(([data]) => {
      if (this.updateInboxOnThreadTypeFilter(data.args)) {
        if (
          !isBlank(data.args) &&
          !isBlank(data.args.chatroomId) &&
          ((!isBlank(chatroom[1]) &&
            ((Number(data.args.chatroomId) === Number(chatroom[1]) && !this.hideMessageCount) ||
              Number(data.args.chatroomId) !== Number(chatroom[1]))) ||
            isBlank(chatroom[1]))
        ) {
          this.hideMessageCount = false;
        }

        if (
          data.args.chatroomId &&
          data.args.messageId &&
          data.args.localpushData &&
          isPresent(data.args.localpushData.pushDeepLink) &&
          this.platform.is('capacitor')
        ) {
          this.userData.notificationSoundName = data.args.localpushData?.soundname;
          this.pushLocalNotification({
            data: {
              ...data.args.localpushData,
              'user-data': JSON.stringify({ pushDeepLink: data.args.localpushData.pushDeepLink })
            },
            id: String(data.args.messageId)
          });
        }
        this.socketService.emitEvent(Socket.messagePollingAck, {
          userid: this.userData.userId,
          displayName: this.userData.displayName,
          chatroomId: data && data.args && data.args.chatroomId ? data.args.chatroomId : 0,
          messageId: data && data.args && data.args.messageId ? data.args.messageId : 0,
          type: data && data.args && data.args.type ? data.args.type : '',
          uniqueDeviceId: this.platform.is('capacitor') ? localStorage.getItem(Constants.storageKeys.deviceID) : '' // this.platformValue != 'web' ? device.uuid Need to get this value
        });
        if (Number(chatroom[1]) && data.args.removeChatroomFromListOnPolling) {
          this.chatroomUserCountUpdated.next({ chatRoomId: Number(chatroom[1]) });
        }
        if (this.messageList) {
          let chatRoomId = Number(data.args.chatroomId);
          let messageIndex = this.getChatroomIndex(chatRoomId);
          if (data.args.baseId && messageIndex === -1) {
            chatRoomId = Number(data.args.baseId);
            messageIndex = this.getChatroomIndex(chatRoomId);
          }
          if (messageIndex !== -1) {
            if (data.args.customData && data.args.customData.action === 'updateInboxTags') {
              let messageTagList = !isBlank(this.messageList[messageIndex].messageTagList) ? this.messageList[messageIndex].messageTagList : [];
              if (data.args.customData && (data.args.customData.inboxTags || data.args.customData.removedInboxTagId)) {
                if (data.args.customData.inboxTags) {
                  messageTagList = [...messageTagList, ...data.args.customData.inboxTags.map((x) => ({ ...x, type: TagType.THREAD }))];
                } else {
                  messageTagList = messageTagList.filter(
                    (tag) => tag.id !== Number(data.args.customData.removedInboxTagId) && tag.type === TagType.THREAD
                  );
                }
              }
              if (data.args.customData && (data.args.customData.messageTags || data.args.customData.removedMessageTagId)) {
                if (data.args.customData.messageTags) {
                  messageTagList = [...messageTagList, ...data.args.customData.messageTags.map((x) => ({ ...x, type: TagType.MESSAGE }))];
                } else {
                  messageTagList = messageTagList.filter(
                    (tag) => tag.id !== Number(data.args.customData.removedMessageTagId) && tag.type === TagType.MESSAGE
                  );
                }
              }
              this.messageList[messageIndex].messageTagList = messageTagList;
              this.messageListUpdated.next({
                message: this.messageList[messageIndex],
                incrementCount: 0,
                chatThreadType: data?.args?.chatThreadType ? data.args.chatThreadType : ''
              });
            } else {
              const { unreadCount } = this.messageList[messageIndex];
              if (data.args.meessageFromMaskedChild && this.messageList[messageIndex].messageType === +Constants.messageListTypes.masked) {
                this.messageList[messageIndex].hasUnreadMessages = true;
                this.messageList[messageIndex].maskedUnreadCount =
                  this.messageList[messageIndex]?.maskedUnreadCount && Number(this.messageList[messageIndex]?.maskedUnreadCount) > 0
                    ? Number(this.messageList[messageIndex].maskedUnreadCount) + 1
                    : 1;
                this.messageList[messageIndex].deliveryTime = data.args?.sentTime;
                let childMessages = this.messageList[messageIndex]?.maskedReplyMessages || [];
                let childMessageIndex = childMessages.findIndex((item: any) => Number(item.chatroomid) === Number(data.args.chatroomId));
                if (childMessageIndex !== -1) {
                  this.updateMessageDetailsFromSocket(childMessages[childMessageIndex], data);
                  this.messageListUpdated.next({
                    message: this.messageList[messageIndex],
                    incrementCount: 1,
                    chatThreadType: data?.args?.chatThreadType ? data.args.chatThreadType : ''
                  });
                } else {
                  this.fetchReplyMessagesofMasked(chatRoomId).subscribe((response) => {
                    childMessages = response.message;
                    this.messageList[messageIndex].maskedSubCount += 1;
                    this.messageList[messageIndex].maskedReplyMessages = childMessages;
                    childMessageIndex = childMessages.findIndex((item: any) => Number(item.chatroomid) === Number(data.args.chatroomId));
                    this.updateMessageDetailsFromSocket(childMessages[childMessageIndex], data, false);
                    this.messageListUpdated.next({
                      message: this.messageList[messageIndex],
                      incrementCount: 1,
                      chatThreadType: data?.args?.chatThreadType ? data.args.chatThreadType : ''
                    });
                  });
                }
              } else if (data.args.removeChatroomFromListOnPolling) {
                const message = deepCopyJSON(this.messageList[messageIndex]);
                const unread = message.unreadCount;
                this.messageList.splice(messageIndex, 1);
                this.messageListUpdated.next({
                  message,
                  incrementCount: +unread * -1,
                  removeThread: true,
                  chatThreadType: data?.args?.chatThreadType ? data.args.chatThreadType : ''
                });
              } else {
                this.updateMessageDetailsFromSocket(this.messageList[messageIndex], data);
                this.messageListUpdated.next({
                  message: this.messageList[messageIndex],
                  incrementCount: 1,
                  chatThreadType: data?.args?.chatThreadType ? data.args.chatThreadType : ''
                });
              }
              const incrementCount = data?.args?.removeChatroomFromListOnPolling ? Number(unreadCount) * -1 : 1;
              this.sortMessageList(true, incrementCount);
            }
          } else {
            this.reloadForNewChatroomPollling = 0;
            this.addNewChatFromAPI(chatRoomId.toString(), data.args.messageId);
          }
        }
      }
    });

    // Message delete / restore
    this.socketService.subscribeEvent(Socket.updateChatMessageDeleteStatus).subscribe((args) => {
      if (args[0].chatroomId && args[0].messageId) {
        this.httpService
          .doPost({
            endpoint: APIs.fetchMessages,
            payload: {
              filter: {
                chatroomId: +args[0].chatroomId
              }
            },
            contentType: 'json',
            parseToString: false,
            version: Constants.apiVersions.apiV5,
            loader: false
          })
          .subscribe((response: any) => {
            if (response.success && response.data && response.data.message) {
              const { data } = response;
              if (this.messageList) {
                let chatRoomId = Number(data.message.chatroomId);
                let messageIndex = this.getChatroomIndex(chatRoomId);
                if (data.message.baseChatroomId && messageIndex === -1) {
                  chatRoomId = Number(data.baseId);
                  messageIndex = this.getChatroomIndex(chatRoomId);
                }
                if (messageIndex !== -1) {
                  this.messageList[messageIndex].messageStatus = data.message.messageStatus;
                  this.messageList[messageIndex].messageDeletedTime = data.message.messageDeletedTime;
                  this.messageList[messageIndex].message = data.message.message;
                  this.messageListUpdated.next({
                    message: this.messageList[messageIndex],
                    incrementCount: 0,
                    chatThreadType: data?.chatThreadType ? data.chatThreadType : ''
                  });
                }
              }
            }
          });
      }
      if (args[0].isChildThread && args[0].parentChatRoomId) {
        this.fetchReplyMessagesofMasked(args[0].parentChatRoomId).subscribe((response) => {
          const { message: childMessages } = response;
          // Get indexes
          const messageIndex = this.getChatroomIndex(args[0].parentChatRoomId);
          const childMessageIndex = childMessages.findIndex(
            (item: any) => +item.id === +args[0].messageId && +item.chatroomId === +args[0].chatroomId
          );
          // Ensure indexes are valid before updating
          if (messageIndex !== -1 && childMessageIndex !== -1) {
            const childMessage = childMessages[childMessageIndex];
            const targetMessage = this.messageList[messageIndex].maskedReplyMessages[childMessageIndex];
            // Update targetMessage properties
            Object.assign(targetMessage, {
              message: childMessage.message,
              messageDeletedTime: childMessage.messageDeletedTime,
              messageStatus: childMessage.messageStatus
            });
          }
        });
      }
      this.$messageDeleteRestore.next(args);
    });
  }
  enableFormPolling() {
    if (this.isEnableConfig(Config.enableFormCenter)) {
      this.socketService.subscribeEvent(Socket.formPolling).subscribe((value: any) => {
        this.getMessageFormCounts(Constants.countTypes.forms, true);
      });
    }
  }

  enableDocumentPolling() {
    if (this.isEnableConfig(Config.enableDocCenter)) {
      this.socketService.subscribeEvent(Socket.obtainSignPolling).subscribe((value) => {
        this.documentPollingEvent.emit(value);
        const firstItem = value[0];
        if (
          isPresent(firstItem) &&
          (isPresent(firstItem.message) ||
            firstItem.signatureStatus === Signature.signatureStatus.signatureSignedStatus ||
            isPresent(firstItem.notifyOnSubmit))
        ) {
          this.documentCountUpdated.next(0);
        }
      });
    }
  }

  isOfflineFormsEnabled() {
    return this.isEnableConfig(Config.enableOfflineForms) && this.platform.is('capacitor') && !this.loggedUserIsPatient();
  }

  get isMultiAdmissionsEnabled() {
    return this.isEnableConfig(Config.enableMultiAdmissions);
  }
  get showAdmissionTabInProfile() {
    return this.isMultiAdmissionsEnabled && this.loggedUserIsPatient();
  }

  /**
   * Adds a new chat from the API.
   *
   * @param chatRoomId - The ID of the chat room.
   * @param messageId - The ID of the message (optional).
   */
  addNewChatFromAPI(chatRoomId: string, messageId?: number): void {
    const ackData = {
      userid: this.userData.userId,
      displayName: this.userData.displayName,
      chatroomId: chatRoomId,
      messageId: messageId || 0,
      status: Constants.newMessageAckOnPollingStatus.success
    };
    this.httpService
      .doPost({
        endpoint: APIs.fetchMessages,
        payload: {
          filter: {
            chatroomId: +chatRoomId
          }
        },
        contentType: 'json',
        parseToString: false,
        version: Constants.apiVersions.apiV5,
        loader: false
      })
      .subscribe({
        next: ({ data, success }: GeneralResponse<MessagethreadOnPolling>) => {
          const { message } = data;
          if (isPresent(message) && isPresent(message.chatroomId) && success) {
            if (Number(message.baseId) !== 0) {
              const existingMessageIndex = this.getChatroomIndex(message.chatroomId);
              if (existingMessageIndex > -1) {
                this.messageList.splice(existingMessageIndex, 1);
              }
            }
            this.socketService.emitEvent(Socket.messagePollingNewThreadAck, ackData);
            this.messageList.unshift(message);
            this.messageListUpdated.next({ message, incrementCount: 1 });
            this.sortMessageList(true, Number(message?.unreadCount));
          } else if (this.reloadForNewChatroomPollling < 3) {
            ackData.status = Constants.pollingAckStatus.retry;
            this.socketService.emitEvent(Socket.messagePollingNewThreadAck, ackData);
            this.reloadForNewChatroomPollling += 1;
            this.addNewChatFromAPI(chatRoomId, messageId);
          } else {
            ackData.status = Constants.pollingAckStatus.unknown;
            this.socketService.emitEvent(Socket.messagePollingNewThreadAck, ackData);
          }
        },
        error: () => {
          ackData.status = Constants.pollingAckStatus.failedWithoutRetry;
          this.socketService.emitEvent(Socket.messagePollingNewThreadAck, ackData);
        }
      });
  }
  /**
   * getChatroomIndex to find message index.
   * @param chatRoomId number
   * @returns number
   */
  getChatroomIndex(chatRoomId: number): number {
    return this.messageList?.findIndex((item: MessagesResponse) => +item.chatroomId === +chatRoomId) ?? -1;
  }
  childMessageUpdated(baseId: number): void {
    if (this.messageList) {
      const messageIndex = this.getChatroomIndex(baseId);
      if (messageIndex > -1) {
        let totalChildUnreadCountBefore = 0;
        let totalChildUnreadCountAfter = 0;
        this.messageList[messageIndex]?.maskedReplyMessages?.map((m) => {
          totalChildUnreadCountBefore += m.unreadCount;
        });
        this.fetchReplyMessagesofMasked(baseId).subscribe((response) => {
          this.messageList[messageIndex].maskedReplyMessages = response?.message;
          if (response?.message?.length > 0) {
            this.messageList[messageIndex].messageOrder = response?.message[0]?.sent;
          }
          response?.message?.forEach((m) => {
            totalChildUnreadCountAfter += m.unreadCount;
          });
          this.messageList[messageIndex].maskedUnreadCount -= totalChildUnreadCountBefore - totalChildUnreadCountAfter;
          this.messageList[messageIndex].maskedUnreadCount =
            this.messageList[messageIndex].maskedUnreadCount > 0 ? this.messageList[messageIndex].maskedUnreadCount : 0;
          this.sortMessageList(false);
        });
      }
    }
  }

  updateMessageDetailsFromSocket(message: MessagesResponse, data: any, incrementCount = true): void {
    message.message = data.args.message;
    message.messagePriorityUnread =
      Number(message.messagePriorityUnread) !== 0 && Number(message.messagePriorityUnread) < data.args.priorityId
        ? message.messagePriorityUnread
        : data.args.priorityId;
    if (!message.messageMentionUnread) {
      message.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
    }
    message.hasUnreadMessages = true;
    message.messageOrder = data.args.sentTime;
    if (incrementCount) {
      message.unreadCount = +message.unreadCount + 1;
    }
    if (data.args.senderUserDetails) {
      // TODO Polling data update with new response structure
    }
  }

  getUnreadMention(mentionedUsers: number[]): boolean {
    return mentionedUsers && mentionedUsers.includes(Number(this.userData.userId));
  }

  sortMessageList(incrementCount = true, count?): void {
    this.messageList.sort((a, b) => {
      const sentComparison = Number(b.messageOrder) - Number(a.messageOrder);
      if (a.pinnedStatus && !b.pinnedStatus) {
        return -1;
      }
      if (!a.pinnedStatus && b.pinnedStatus) {
        return 1;
      }
      return sentComparison;
    });
    if (!this.messageCount) {
      this.getMessageFormCounts(Constants.countTypes.messages);
    } else if (incrementCount) {
      if (isPresent(count)) {
        this.messageCount = this.messageCount + Number(count) < 0 && Number(count) < 0 ? 0 : this.messageCount + Number(count);
      } else {
        this.messageCount += 1;
      }
    }
    this.messageListUpdated.next({ incrementCount: isPresent(count) ? Number(count) : 1 });
  }

  joinAppSocket(reInit = false): void {
    const socketData = {
      user: this.userData.userId,
      name: this.userData.displayName,
      tenantId: this.userData?.tenantId,
      tenantKey: this.userData.tenantKey,
      tenantName: this.userData.tenantName,
      avatar: this.userData.profileImageThumbUrl,
      ver: '2.7',
      application: 'mobile',
      calledFrom: Constants.loginSuccessSocket,
      sites: this.userData.mySites
    };

    if (!this.socketService.status) {
      this.socketService.connectSocket();
      return;
    }
    this.socketService.emitEvent(
      Socket.joinToApp,
      socketData,
      (socketStatus: boolean, activeLoginSessions: number, scheduleDataToUpdate: any, socketClientId: string) => {
        if (activeLoginSessions && !this.userData.appLessSession) {
          this.trackLoginActivity(activeLoginSessions);
        }
        if (socketClientId) {
          this.socketClientId = socketClientId;
        }
      }
    );
    this.enableMessageSocket();
    this.enableFormPolling();
    this.enableDocumentPolling();
    this.enableConfigSocket();

    if (this.platform.is('capacitor')) {
      const userPushRegistration = localStorage.getItem(Constants.storageKeys.userPushDeviceRegistration);
      if (reInit) {
        const userPushRegistrationData = deepParseJSON(userPushRegistration);
        if (userPushRegistrationData) {
          this.emitUserPushRegistrationEvent(userPushRegistrationData);
        }
      }
      if (!userPushRegistration) {
        this.nativePushNotificationEnable();
      }
    } else {
      this.webPushNotificationPermission();
    }
  }

  trackLoginActivity(activeLoginSessions: number): void {
    const data = {
      userName: this.userData.userName || this.userData.username,
      activeLogins: activeLoginSessions
    };

    // TODO: Active logins
    this.trackActivity({
      type: Activity.userAccess,
      name: Activity.userLogin,
      des: { data, desConstant: Activity.login }
    });
  }

  enableConfigSocket(): void {
    this.socketService.subscribeEvent(Socket.updateConfigPolling).subscribe(([config]) => {
      if (config.configurationType === 'updateTenant') {
        config.tenantData.forEach((element) => {
          if (this.userData.config[element.configuration_key] !== undefined) {
            this.userData.config[element.configuration_key] = element.configuration_value;
          }
        });
        if (config?.siteConfigs) {
          try {
            const siteConfigs = JSON.parse(config.siteConfigs);
            if (typeof siteConfigs === 'object' && siteConfigs !== null) {
              this.userData.siteConfigs = { ...this.userData.siteConfigs, ...siteConfigs };
            }
          } catch (error) {
            console.error(error);
          }
        }
      } else if (config.configurationType === 'updatePrivilage') {
        this.userData.privileges = config.privilageData;
        this.setPermissions(this.userData.privileges);
      }
      this.configValuesUpdated.next(null);
    });
  }

  enablePushNotificationSocket(): void {
    this.socketService.subscribeEvent(Socket.pushNotifyToUser).subscribe(([pushData]) => {
      const pushDeepLink = isPresent(pushData?.push_deep_link) && stringToJSON(pushData.push_deep_link);
      const paramOptions: NotificationOptions = {
        icon: '../../assets/pwa/icon-72x72.png',
        body: pushData.data,
        requireInteraction: true,
        data: JSON.stringify({
          pushDeepLink: pushDeepLink ? pushDeepLink : { state: '' }
        })
      };
      const notification = new Notification(theme.name, paramOptions);
      const sound = Constants.notificationSoundList.find((sound) => sound.name.toLowerCase() === this.userData.notificationSoundName);
      if (typeof AudioContext !== 'undefined') {
        const audioContext = new AudioContext();
        fetch(`../../../assets/audio/${sound.url}`)
          .then((response) => response.arrayBuffer())
          .then((data) => {
            return audioContext.decodeAudioData(data);
          })
          .then((buffer) => {
            const source = audioContext.createBufferSource();
            source.buffer = buffer;
            source.connect(audioContext.destination);
            source.start(0);
          })
          .catch((error) => {
            console.error('Error decoding audio data:', error);
          });
      }
      notification.addEventListener('click', (event: any) => {
        const data = event.target.data;
        this.addRouteLink(data);
      });
    });
  }

  fetchUsers(reqData: FetchUsersExtraParams, loader = true, isPdg = false): Observable<any> {
    const url = APIs.getUsersByTenantRole;
    let body: any = {
      roleId: 3, // TODO: Need to check this value
      status: Constants.notRejected,
      excludeRoleId: true, // TODO: Need to check this value
      isTenantRoles: null, // TODO: Need to check this value
      needVirtualPatients: isPdg || this.isEnableConfig(Config.enableApplessVideoChat), // TODO: Need to check this value
      siteIds: '0', // TODO: Need to check this value
      folder: null, // TODO: Need to check this value
      userGroup: this.userData.group, // TODO: Need to check this value
      isFromChat: 1, // TODO: Need to check this value
      pageCount: Constants.defaultPageCount,
      searchKeyword: '',
      nursingAgencies: this.userData?.nursing_agencies || ''
    };
    body = Object.assign(body, reqData);
    if (this.userData?.accessSecurityEnabled) {
      body = {
        ...body,
        accessSecurityEnabled: this.userData.accessSecurityEnabled,
        accessSecurityEsiValue: this.userData.accessSecurityEsiValue,
        accessSecurityIdentifierType: this.userData.accessSecurityIdentifierType,
        accessSecurityType: this.userData.accessSecurityType
      };
    }
    return this.httpService.doGet({ endpoint: url, extraParams: body, loader });
  }

  startNewChat(patientId?: number, admissionId?: number): any {
    const url = APIs.startNewChat;
    let body: any = {};
    if (isPresent(patientId) && +this.userData.userId !== +patientId) {
      body = {
        associatedPatientId: +patientId // need value incase of caregiver
      };
    }
    // send selected admission id for patient
    if (isPresent(admissionId)) {
      body = { ...body, ...{ admissionId } };
    }
    return this.httpService.doPost({ endpoint: url, payload: body, contentType: 'form', loader: true });
  }
  /**
   * newChatAction call start-new-chat api and present role/user list or redirect to chat room based on response.
   * @param chooseRecipientsPage
   * @param selectedAssociatePatient
   */
  newChatAction(chooseRecipientsPage: typeof ChooseRecipientsPage, selectedAssociatePatient: number = undefined, admissionId: any = undefined): void {
    this.startNewChat(selectedAssociatePatient, admissionId).subscribe((res) => {
      this.isLoading = false;
      if (res.staffLists) {
        this.presentNewMessageModal(chooseRecipientsPage, res.staffLists, {
          associatedPatient: selectedAssociatePatient,
          admissionId
        }); // Multiple staffs
      } else if (res.data?.roleList) {
        this.presentNewMessageModal(chooseRecipientsPage, [], {
          roleList: res.data.roleList,
          associatedPatient: selectedAssociatePatient,
          admissionId
        }); // Multiple roles
      } else if (res?.data) {
        this.nextAction(res?.data);
      }
    });
  }

  getMessageFormCounts(countType = Constants.countTypes.all, isPolling = false): void {
    const params = {
      type: countType
    };
    this.httpService.doGet({ endpoint: APIs.messageFormCount, extraParams: params, loader: false }).subscribe(
      (count) => {
        if (count.messages) {
          this.messageCount = Number(count.messages);
        }
        if (count.forms) {
          this.formIndividualCounts = {
            pending: count.forms.pending ? Number(count.forms.pending) : 0,
            completed: count.forms.completed ? Number(count.forms.completed) : 0,
            archived: count.forms.archived ? Number(count.forms.archived) : 0,
            draft: count.forms.draft ? Number(count.forms.draft) : 0
          };
          this.formCount =
            this.formIndividualCounts.pending +
            this.formIndividualCounts.completed +
            this.formIndividualCounts.archived +
            this.formIndividualCounts.draft;
        }
        this.messageFormCountUpdated.next({ countType, isPolling });
      },
      (error) => {
        this.errorHandler(error);
      }
    );
  }
  /** @deprecated  Remove after API changes */
  addAdditionalKeyValues(arr: any[]): any[] {
    if (isBlank(arr)) {
      return [];
    }
    // TODO Masked message
    return arr.map((x: any) => ({
      ...x,
      chatHeading: this.displayNameFetch(x),
      chatroomId: x.chatroomid,
      chatSubHeading: this.middleContentFetch(x),
      message: x.message,
      chatAvatar: this.avatarFetchMaskedChild(x),
      isSelfMessage: this.isSameUser(x),
      hasUnreadMessages: +x.unreadCount > 0,
      messageForwarded: x.forwardName ? `${this.common.getTranslateData('LABELS.FORWARDED_TO')} ${x.forwardName}` : '',
      chatroomFlag: +x.thread_flag,
      messageFlag: +x.msg_flag,
      messagePriorityUnread: +x.unReadPriority,
      messagePriorityRead: +x.readPriority,
      messageMentionUnread: +x.unReadMention,
      messageMentionRead: +x.readMention
    }));
  }
  /** @deprecated  Remove after API changes */
  middleContentFetch(x: any): string {
    if (!this.isSameUser(x)) {
      if (
        (isPresent(x.patient_caregiver_displayname) && !this.loggedUserIsPatient()) ||
        (Number(x.grp) === 3 && this.loggedUserIsPatient() && isPresent(x.caregiver_displayname))
      ) {
        return this.caregiverOrRelationLabel(x);
      }
      return !this.loggedUserIsPatient() && isPresent(x.patient_chatWith) ? this.patientLabel : x.role;
    }
    if (
      (isPresent(x.patient_caregiver_displayname) && !this.loggedUserIsPatient()) ||
      (this.loggedUserIsPatient() && isPresent(x.chatWith_caregiver_displayname))
    ) {
      return this.caregiverOrRelationLabel(x);
    }
    return !this.loggedUserIsPatient() && isPresent(x.patient_chatWith) ? this.patientLabel : x.chatWithRole;
  }
  /** @deprecated  Remove after API changes */
  caregiverOrRelationLabel(message: any): string {
    if (
      this.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact &&
      message.message_group_id === Constants.messageListTypes.general &&
      message.messageType !== Constants.messageListTypes.broadcast &&
      message.messageType !== Constants.messageListTypes.masked
    ) {
      return message.patient_chatWith_relation;
    }
    return this.caregiverLabel;
  }
  /** @deprecated  Remove after API changes */
  displayNameFetch(x: any): string {
    const fromName = !this.isSameUser(x) ? x.fromName : x.chatWith;
    const chatWith = !this.loggedUserIsPatient() && isPresent(x.patient_chatWith) ? x.patient_chatWith : fromName;
    const chatWithCaregiver =
      !this.loggedUserIsPatient() && isPresent(x.patient_caregiver_displayname) ? x.patient_caregiver_displayname : x.chatWith_caregiver_displayname;
    return isPresent(chatWithCaregiver) && this.isSameUser(x) ? `${chatWithCaregiver} (${chatWith})` : chatWith;
  }
  /** @deprecated  Remove after API changes */
  avatarFetchMaskedChild(x: any): string {
    if (!this.isSameUser(x)) {
      return !this.loggedUserIsPatient() && x.patient_userid ? x.patient_chatWith_avatar : x.fromAvatar;
    }
    return !this.loggedUserIsPatient() && x.patient_userid ? x.patient_chatWith_avatar : x.chatWithAvatar;
  }
  /** @deprecated  Remove after API changes */
  isSameUser(x: any): boolean {
    return Number(this.userData?.userId) === Number(x.userid);
  }
  fetchReplyMessagesofMasked(chatRoomId: number, archived = false): Observable<any> {
    const url = APIs.getMaskedReplyMessages;
    const payload = {
      chatRoomId,
      pageCount: 0,
      archived: archived ? 1 : 0
    };
    return this.httpService
      .doPost({
        endpoint: url,
        payload,
        contentType: 'form',
        parseToString: true,
        loader: false
      })
      .pipe(map((response: any) => ({ message: this.addAdditionalKeyValues(response.message) })));
  }
  searchTenantIds(): any {
    let searchTenantIds = '';
    this.userData?.crossTenantsDetails?.forEach((tenant: any) => {
      searchTenantIds += `${tenant.id},`;
    });
    return searchTenantIds.slice(0, -1);
  }

  newMessageAction(
    chooseRecipientsPage: typeof ChooseRecipientsPage,
    choosePatientComponent: typeof ChoosePatientComponent,
    admissionComponent: typeof AdmissionComponent
  ): void {
    if (this.loggedUserIsPatient() && this.userData.roleName === Constants.userPatient) {
      // Patient
      this.admissionForSinglePatient(this.userData.userId, PatientType.PATIENT, chooseRecipientsPage, admissionComponent);
    } else if (this.loggedUserIsPatient() && isPresent(this.userData?.alternate_contact_patient)) {
      // Caregiver
      if (this.userData.alternate_contact_patient?.length > 1) {
        // Multiple associated patients
        this.presentChoosePatientModal(choosePatientComponent, chooseRecipientsPage, admissionComponent);
      } else {
        // Single associated patient
        this.selectedAssociatePatient = this.userData.alternate_contact_patient[0].userId;
        this.admissionForSinglePatient(
          this.userData.alternate_contact_patient[0].userId,
          PatientType.SINGLE_ASSOCIATED_PATIENT,
          chooseRecipientsPage,
          admissionComponent
        );
      }
    } else {
      this.presentNewMessageModal(chooseRecipientsPage, ''); // Staff
    }
    this.selectedAssociatePatient = '';
  }
  admissionForSinglePatient(
    patientId: string,
    userType: PatientType,
    chooseRecipientsPage: typeof ChooseRecipientsPage,
    admissionComponent: typeof AdmissionComponent
  ): void {
    if (this.isMultiAdmissionsEnabled) {
      this.admissionService.getAdmissionList({ patientId, from: 'PatientStartNewChat' }).subscribe(({ content }: AdmissionList) => {
        if (content.length > 1) {
          this.selectAdmission(
            { userId: patientId, userType, from: 'PatientStartNewChat', admissions: content },
            chooseRecipientsPage,
            admissionComponent
          );
        } else if (content.length === 1) {
          this.newChatAction(chooseRecipientsPage, +patientId, content[0].admissionId);
        }
      });
    } else {
      this.newChatAction(chooseRecipientsPage, +patientId);
    }
  }
  async presentNewMessageModal(
    chooseRecipientsPage: typeof ChooseRecipientsPage,
    staffList: any,
    extra: {
      associatedPatient?: number;
      roleList?: any;
      admissionId: string;
    } = { associatedPatient: undefined, roleList: [], admissionId: undefined }
  ): Promise<void> {
    const modal = await this.modalController.create({
      id: 'newMessage',
      componentProps: { staffList, roleList: extra.roleList, associatedPatient: extra.associatedPatient, admissionDetails: {admissionId: extra.admissionId} },
      component: chooseRecipientsPage
    });
    return await modal.present();
  }

  async presentChoosePatientModal(
    choosePatientComponent: typeof ChoosePatientComponent,
    chooseRecipientsPage: typeof ChooseRecipientsPage,
    admissionComponent?: typeof AdmissionComponent
  ): Promise<void> {
    const modal = await this.modalController.create({
      component: choosePatientComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.selectedAssociatePatient = data.userId;
        if (this.isMultiAdmissionsEnabled) {
          this.selectAdmission(
            { userId: this.selectedAssociatePatient.toString(), userType: PatientType.SINGLE_ASSOCIATED_PATIENT, from: 'PatientStartNewChat' },
            chooseRecipientsPage,
            admissionComponent
          );
        } else {
          this.newChatAction(chooseRecipientsPage, this.selectedAssociatePatient);
        }
      }
    });
    return await modal.present();
  }
  nextAction(response: any): void {
    if (response.chatRoomId) {
      this.updateChatRoomId(this.roomID, response.chatRoomId);
      this.router.navigate([`message-center/messages/active/chat/${response.chatRoomId}`]);
    } else if (response?.message) {
      this.common.showMessage(response.message);
    }
  }

  getPriority = (priorityFilterValue: number): string => {
    let className = 'alert-fill bg-default';
    if (priorityFilterValue === MessagePriority.HIGH) {
      className = 'alert-fill danger';
    } else if (priorityFilterValue === MessagePriority.NORMAL) {
      className = 'chat-outline primary';
    } else if (priorityFilterValue === MessagePriority.LOW) {
      className = 'arrowdown-fill primary';
    }
    return className;
  };

  async presentActionSheet(
    config: { message?: any; index?: number; flagApiType?: FlagApiType; ActionType?: ActionType },
    callback: any
  ): Promise<void> {
    let hideClearFlag = true;
    if (config.flagApiType === 'thread' && config.message?.chatroomFlag !== Constants.flagTypes.noFlag) {
      hideClearFlag = false;
    } else if (config.flagApiType === 'msg' && Number(config.message?.msg_flag) !== Constants.flagTypes.noFlag) {
      hideClearFlag = false;
    }
    let headerText = 'TITLES.CHOOSE_FLAG';
    if (config.ActionType === 'priority') {
      headerText = 'TITLES.CHOOSE_PRIORITY';
    }
    const actionSheet = await this.actionSheetController.create({
      header: this.common.getTranslateData(headerText),
      mode: 'ios',
      buttons: [
        {
          text: this.common.getTranslateData('OPTIONS.HIGH'),
          handler: () => {
            if (config.ActionType === 'priority') {
              callback(MessagePriority.HIGH);
            } else {
              this.doFlag({
                message: config.message,
                i: config.index,
                flagType: Constants.flagTypes.high,
                flagApiType: config.flagApiType
              }).then(() => callback(Constants.flagTypes.high));
            }
          }
        },
        {
          text: this.common.getTranslateData('OPTIONS.MEDIUM'),
          handler: () => {
            if (config.ActionType === 'priority') {
              callback(MessagePriority.NORMAL);
            } else {
              this.doFlag({
                message: config.message,
                i: config.index,
                flagType: Constants.flagTypes.medium,
                flagApiType: config.flagApiType
              }).then(() => callback(Constants.flagTypes.medium));
            }
          }
        },
        {
          text: this.common.getTranslateData('OPTIONS.LOW'),
          handler: () => {
            if (config.ActionType === 'priority') {
              callback(MessagePriority.LOW);
            } else {
              this.doFlag({
                message: config.message,
                i: config.index,
                flagType: Constants.flagTypes.low,
                flagApiType: config.flagApiType
              }).then(() => callback(Constants.flagTypes.low));
            }
          }
        },
        {
          text: this.common.getTranslateData('OPTIONS.CLEAR_FLAG'),
          cssClass: hideClearFlag ? 'hide-important' : '',
          handler: () => {
            this.doFlag({
              message: config.message,
              i: config.index,
              flagType: Constants.flagTypes.noFlag,
              flagApiType: config.flagApiType
            }).then(() => callback(Constants.flagTypes.noFlag));
          }
        },
        {
          text: this.common.getTranslateData('BUTTONS.CANCEL'),
          role: 'cancel'
        }
      ]
    });
    await actionSheet.present();
  }
  async presentCountryPopover(ev: any, callback: CountryPopoverCallback, pageName?: string): Promise<void> {
    const popover = await this.popoverController.create({
      component: CountryPopoverComponent,
      event: ev,
      cssClass: 'users-popover country-card-popover',
      mode: 'ios',
      side: pageName === VisitScheduleConstants.createVisitPage ? 'bottom' : 'top'
    });
    popover.onDidDismiss().then((result: { data: Country }) => {
      if (!isBlank(result?.data?.code)) {
        this.selectedCountry = result.data;
        callback(this.selectedCountry);
      }
    });
    return popover.present();
  }
  getCountryDetails(countryId: string, code?: string): Country {
    let selectedCountry: Country[];
    const shouldFilterByCode = isPresent(code) && countryDialCodes.some((x) => x.code.toLowerCase() === code.toLowerCase());
    if (shouldFilterByCode) {
      selectedCountry = countryDialCodes.filter((country) => country.code.toLowerCase() === code.toLowerCase());
    } else {
      const countryDialCode =
        countryId !== Constants.defaultCountryId && countryDialCodes.some((x) => x.dialCode === countryId) ? countryId : Constants.defaultCountryId;
      selectedCountry = countryDialCodes.filter((country) => {
        if (countryDialCode === Constants.defaultCountryId) {
          return country.code.toLowerCase() === environment.defaultCountryFlag;
        }
        return country.dialCode === countryDialCode;
      });
    }
    this.selectedCountry = selectedCountry.shift();
    if (isPresent(this.selectedCountry?.code)) {
      this.selectedCountry.code = this.selectedCountry.code.toLowerCase();
    }
    return this.selectedCountry;
  }

  doFlag({
    message,
    i,
    flagApiType,
    flagType
  }: {
    message: any;
    i: number;
    flagType: number;
    flagApiType: FlagApiType;
  }): Promise<void> {
    return new Promise<void>((done) => {
      const body = {
        type: flagApiType,
        chatroom_id: message.chatroomId,
        flag_type_id: flagType,
        msg_id: flagApiType === 'msg' ? message.id : undefined
      };

      this.httpService.doPost({ endpoint: APIs.flagMessage, payload: body }).subscribe((response) => {
        let messagePop = '';
        if (response.status === 1) {
          if (flagType === Constants.flagTypes.noFlag) {
            messagePop =
              flagApiType === 'msg'
                ? this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_FLAG_CLEARED_MESSAGE')
                : this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_FLAG_CLEARED_CHATROOM');
          } else {
            messagePop =
              flagApiType === 'msg'
                ? this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_FLAGGED_MESSAGE')
                : this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_FLAGGED_CHATROOM');
          }
          done();
        } else {
          messagePop =
            flagApiType === 'msg'
              ? this.common.getTranslateData('ERROR_MESSAGES.FAILED_FLAGGED_MESSAGE')
              : this.common.getTranslateData('ERROR_MESSAGES.FAILED_FLAGGED_CHATROOM');
        }
        this.common.showMessage(messagePop);
      });
    });
  }

  getFlagType(flag: number): string {
    switch (+flag) {
      case Constants.flagTypes.high:
        return Constants.flagClass.high;
      case Constants.flagTypes.medium:
        return Constants.flagClass.medium;
      case Constants.flagTypes.low:
        return Constants.flagClass.low;
      default:
        return Constants.flagClass.noFlag;
    }
  }

  loggedUserIsPatient(): boolean {
    return Number(this.userData?.group) === UserGroup.PATIENT;
  }

  loggedUserIsPartner(): boolean {
    return Number(this.userData?.group) === UserGroup.PARTNER;
  }

  getTenantRolesByPrivilege(privilegeKey: any): Observable<any> {
    const url = APIs.getTenantRolesByPrivilege;
    const params = {
      privilegeKey
    };
    if (privilegeKey === Constants.privilegeKey.allowEnrollmentInitiation) {
      return this.httpService.doGet({ endpoint: url, extraParams: params }).pipe(map((response: any) => response));
    } else {
      return this.httpService.doGet({ endpoint: url, extraParams: params }).pipe(map((response: any) => response));
    }
  }

  setPatientSearchReqBody(
    searchText: string,
    offset: number = Constants.defaultOffset,
    limit: number = Constants.defaultLimit,
    operation: string,
    siteIds: any
  ): any {
    const body = {
      firstname: searchText,
      searchText: searchText,
      operation: isBlank(operation) ? Constants.operationSearch : operation,
      offset,
      limit,
      siteIds,
      searchTenantIds: this.searchTenantIds() || this.userData?.tenantId
    };
    return body;
  }

  getMinutes(currentTime: string, scheduleInterval: any): number {
    const currentTimesplit = currentTime.split(':');
    return Number(currentTimesplit[0]) * 60 + Number(currentTimesplit[1]);
  }

  checkSchedule(scheduleArray: any, currentTime: any): number {
    const scheduleInterval = this.localConfig.scheduleInterval;
    const currentMin = this.getMinutes(currentTime, scheduleInterval);
    return scheduleArray.some((result) => {
      const getMin = this.getMinutes(result, scheduleInterval);
      return getMin <= currentMin && currentMin <= getMin + scheduleInterval;
    });
  }

  scheduleSelectionFilter(user: any, isMaster?: boolean): boolean {
    const d = new Date();
    const currentHour = d.getHours();
    const currentMinutes = d.getMinutes();
    const currentInterval = currentHour + ':' + currentMinutes;
    const userData = this.userData;
    let objRes;
    let availableUser;
    if (userData) {
      const scheduleJson = isMaster ? userData.masterSchedulerData[user.userId] : userData.schedulerData[user.userId];
      if (scheduleJson) {
        objRes = JSON.parse(scheduleJson);
        if (objRes) {
          availableUser = this.checkSchedule(objRes, currentInterval);
          if (availableUser) {
            return true;
          } else {
            return false;
          }
        }
      }
    }
  }

  validationMessages(field: any): void {
    if (field === Constants.inviteUserValidationFields.role) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.ROLE_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.siteId) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.SITE_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.firstName) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.FIRST_NAME_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.lastName) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.LAST_NAME_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.staffId) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.STAFF_ID_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.contact) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.EMAIL_OR_PHONE_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.dob) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.DATE_OF_BIRTH_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.mrnForPatients) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.MRN_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.delegatedRole) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.DELEGATED_STAFF_ROLE_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.password) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_VALIDATION_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.confirmPassword) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_CONFIRMATION_REQUIRED');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.userId) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_USERNAME');
      this.common.showMessage(this.validationMessage);
    } else if (field === Constants.inviteUserValidationFields.relation) {
      this.validationMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_RELATION');
      this.common.showMessage(this.validationMessage);
    }
  }

  getUsersByTenantRoleWithTagId(body: {
    isTenantRoles: boolean;
    roleId: any;
    pageCount: number;
    tagsId: number;
    formRecipients: string;
    searchKeyword: string;
    siteIds: string;
    needVirtualPatients: boolean;
    nursingAgencies: string;
    reoleSearchNeeded: boolean;
    status: string;
    admissionId: string;
  }): Observable<any> {
    const url = APIs.getUsersByTenantRole;
    let extraParams = Object.assign(body);
    if (this.userData?.accessSecurityEnabled) {
      extraParams = {
        ...extraParams,
        accessSecurityEnabled: this.userData.accessSecurityEnabled,
        accessSecurityEsiValue: this.userData.accessSecurityEsiValue,
        accessSecurityIdentifierType: this.userData.accessSecurityIdentifierType,
        accessSecurityType: this.userData.accessSecurityType
      };
    }
    return this.httpService.doGet({ endpoint: url, extraParams, loader: true });
  }
  getAssociatedPatientsByTagId(body: any): Observable<any> {
    const url = APIs.getAssociatedPatients;
    if (body.tagId && isBlank(body.tagId)) {
      body.tagId = undefined;
      delete body.tagId;
    } // No need of TagId calling from message center
    if (this.userData?.accessSecurityEnabled) {
      body = {
        ...body,
        accessSecurityEnabled: this.userData.accessSecurityEnabled,
        accessSecurityEsiValue: this.userData.accessSecurityEsiValue,
        accessSecurityIdentifierType: this.userData.accessSecurityIdentifierType,
        accessSecurityType: this.userData.accessSecurityType
      };
    }
    return this.httpService.doGet({ endpoint: url, extraParams: body, loader: true });
  }
  setRootActivity(activityId: number): void {
    if (!this.rootActivity) {
      this.rootActivity = activityId;
    }
  }

  setParentActivity(activityId: number): void {
    this.parentActivity = activityId;
    this.activityHierarchy = (this.activityHierarchy || '') + activityId + (activityId ? '::' : '');
  }

  resetActivity(): void {
    this.rootActivity = undefined;
    this.parentActivity = undefined;
    this.activityHierarchy = undefined;
  }
  /**
   * pageAccess store snapshot and call page access track activity
   * @param subHead
   * @param headerTitle
   */
  pageAccess(headerTitle?: string) {
    const snapshot = this.route.snapshot;
    const pageTitle = !isBlank(snapshot.data && snapshot.data.title)
      ? snapshot.data.title
      : headerTitle
        ? headerTitle
        : '';
    const snapshotData = Object.assign({}, snapshot, {
      data: { title: pageTitle }
    });
    this.saveRouteData(snapshotData);
    this.trackActivity({ type: Activity.pageAccess });
  }
  trackActivity(
    activityData: any = { type: '', name: '', des: '', linkageId: '' },
    config: any = this.localConfig
  ): void {
    const activity = this.setActivityData(activityData);

    const activityRequest = {
      activityName: activity.activityName,
      activityType: activity.activityType,
      activityDescription: activity.activityDescription,
      createdBy: !isBlank(this.userData) ? this.userData.userId : '',
      rootId: this.rootActivity || 0,
      parentId: this.parentActivity || 0,
      activityHierarchy: this.activityHierarchy || '',
      appVersion: ConfigValues.appVersion || '',
      tenantId: !isBlank(this.userData) ? this.userData?.tenantId : '',
      environment: `${environment.alias}`,
      platform: this.platforms,
      testcase: !isBlank(config) ? config.developerMode.testCase : '',
      linkageId: activityData.linkageId || '',
      latitude: this.vcLatitude || VideoCall.vcLat,
      longitude: this.vcLongitude || VideoCall.vcLong,
      location_enabled: this.vcLatitude || this.vcLongitude ? Constants.isGranted.yes : Constants.isGranted.no,
      notification_enabled: activity.remoteNotification,
      manufacturer: this.common.deviceInfo?.manufacturer,
      deviceModel: this.common.deviceInfo?.deviceModel,
      device: this.common.getDeviceType()
    };
    // TODO: Check notification,location,latitude,longitude values.
    // TODO: Activity call confirmation
    this.httpService
      .doPost({
        endpoint: APIs.trackActivity,
        payload: activityRequest,
        contentType: 'form',
        parseToString: true,
        responseType: 'text',
        loader: false
      })
      .subscribe((activityId: any) => {
        if (activityData.name === Activity.userLogout || activityData.name === Activity.sessionTimeout) {
          this.activityHierarchy = '';
        } else if (activityId) {
          this.setRootActivity(+activityId);
          this.setParentActivity(+activityId);
        }
      });
  }

  errorHandler(error: any): void {
    this.isLoading = false;
  }

  getConfiguration(): void {
    const extraParams = {
      bundleIdentifier: theme.bundleIdentifier // TODO: Need to set bundleIdentifier from config page.Static value added now,because app upgrade url not getting from pwa bundleIdentifier.
    };
    this.httpService
      .doGet({
        endpoint: APIs.configuration,
        extraParams,
        hasAuthToken: false
      })
      .subscribe(
        (config) => {
          this.appConfig = config;
          localStorage.setItem(Constants.storageKeys.appConfig, JSON.stringify(this.appConfig));
        },
        (error) => {
          this.errorHandler(error);
        }
      );
  }

  getConfigurationLocal(): void {
    this.localConfig = ConfigValues.config;
    this.localConfig.bundleIdentifier = theme.bundleIdentifier;
    localStorage.setItem(Constants.storageKeys.localConfig, JSON.stringify(this.localConfig));
  }

  setPermissions(privileges: string): void {
    const permissions = privileges.split(',');
    this.ngxPermissionsService.loadPermissions(permissions);
    this.userPermissions = permissions;
  }

  saveRouteData(routeData: any): void {
    const title = !isBlank(routeData.data.title) ? this.common.getTranslateData(routeData.data.title) : '';
    routeData.data.title = title;
    const data = routeData.data;
    const routerState = routeData._routerState;

    this.routeHistory = [...this.routeHistory, { data, url: routerState.url }];
    if (this.routeHistory.length > 0) {
      // TODO! Remove duplicates value from routeHistory
      const uniqueObjects = [...new Map(this.routeHistory.map((item) => [item.url, item])).values()];
      this.routeHistory = uniqueObjects;
    }

    sessionStorage.setItem(Constants.storageKeys.routeHistory, JSON.stringify(this.routeHistory));
  }
  resetSessionProfileData(userData: any): any {
    this.userData = userData;
  }
  setActivityData(activityData: any): any {
    let activity = {};
    let activityName = activityData.name;
    let activityType = activityData.type;
    let activityDescription;
    if (!activityData.des || typeof activityData.des === 'string') {
      activityDescription = activityData.des;
    } else {
      activityDescription = this.setActivityDescription(activityData.des.data, activityData.des.desConstant);
    }
    let previousPage = '';
    let currentPage = '';
    const routes = !isBlank(this.routeHistory) ? this.routeHistory.length : 0;
    if (routes === 1) {
      previousPage = Activity.start;
      currentPage = `${this.routeHistory[routes - 1].data.title} (${this.routeHistory[routes - 1].url})`;
    } else if (routes > 1) {
      previousPage = `${this.routeHistory[routes - 2].data.title} (${this.routeHistory[routes - 2].url})`;
      currentPage = `${this.routeHistory[routes - 1].data.title} (${this.routeHistory[routes - 1].url})`;
    }
    switch (activityData.type) {
      case Activity.pageAccess: {
        activityName = activityName ? activityName : Activity.pageNavigation;
        activityType = activityType ? activityType : Activity.pageAccess;
        const description = { previousPage, currentPage };

        activityDescription = this.setActivityDescription(description, Activity.page);
        this.currentPage = currentPage;
        break;
      }
      case Activity.exceptionHandler: {
        activityName = activityName ? activityName : Activity.apiException;
        activityType = activityType ? activityType : Activity.exceptionHandler;
        break;
      }
      default:
        break;
    }
    const remoteNotification = this.isPermissionGranted ? Constants.isGranted.yes : Constants.isGranted.no;
    const locationLetLong = this.vcLatitude || '';
    const activityDescValue = {
      description: activityDescription,
      date: new Date(),
      bundle: theme.bundleIdentifier,
      status: remoteNotification === Constants.isGranted.yes ? Constants.enabled : Constants.disabled,
      location: locationLetLong ? Constants.enabled : Constants.disabled
    };
    activityDescription = this.setActivityDescription(activityDescValue, Activity.mainActivityDescription);

    activity = {
      activityName,
      activityType,
      activityDescription,
      remoteNotification
    };
    return activity;
  }
  setSessionTimeout(): void {
    // this.idle.setAutoResume(AutoResume.idle);
    this.idle.onIdleEnd.subscribe(() => {
      // do nothing
    });
    this.idle.onTimeout.subscribe(() => {
      if (!this.userData.appLessSession) {
        this.onTimeOut();
      }
    });
    this.idle.onIdleStart.subscribe(() => {
      // do nothing
    });
    this.idle.onTimeoutWarning.subscribe((countdown) => {
      if (!this.userData.appLessSession) {
        this.onTimeOutWarning(countdown);
      }
    });
    this.keepalive.onPing.subscribe(() => {
      this.onPing();
    });
    if (this.isUserLoggedIn()) {
      this.setSessionTimeoutTime();
    }
  }
  /**
   * onPing trigger on keep alive interval
   */
  onPing(): void {
    // Call presence API with userLastActivityTimeStamp as payload.
  }
  /**
   * onTimeOut trigger at end of warning timeout
   */
  onTimeOut(): void {
    if (this.isUserLoggedIn()) {
      this.idle.stop();
      if (this.platform.is('capacitor')) {
        if (this.isAppMinimize) {
          this.setSessionTimeoutCommonCode();
          if (this.sessionModal) {
            this.common.dismissPopover();
          }
        } else if (this.sessionModal) {
          this.popoverController.dismiss(false);
        } else {
          if (this.browser) {
            this.executeSaveAsDraft();
          } else {
            this.setSessionTimeoutCommonCode();
          }
        }
      } else {
        if (this.sessionModal) {
          this.popoverController.dismiss(false);
        } else {
          this.setSessionTimeoutCommonCode();
        }
      }
    }
    // go to logout page after  sessionTimeout min idle.
  }
  /**
   * onTimeOutWarning trigger at start of countdown
   * @param countdown number
   */
  onTimeOutWarning(countdown: number): void {
    this.sessionService.min = Math.floor(countdown / 60);
    this.sessionService.sec = countdown % 60;
    if (countdown === this.warningTimeout && this.isUserLoggedIn()) {
      this.idle.clearInterrupts();
    }
    if (
      !this.isAppMinimize &&
      !this.browser &&
      countdown <= this.warningTimeout &&
      this.isUserLoggedIn() &&
      !this.sessionModal
    ) {
      this.showSessionTimeoutModal();
    }
    if (this.browser && this.isUserLoggedIn() && this.warningTimeout >= countdown) {
      const message = this.common.getTranslateDataWithParam('MESSAGES.SESSION_EXPIRE', {
        min: this.sessionService.min,
        sec: this.sessionService.sec
      });
      this.inAppBrowserExecuteAddOnScript({ message, from: Constants.inAppAddOnFromPath.session });
    }
  }
  /**
   * resetSessionTimeout restart timer with default timeout values of that particular session.
   * @param restartTimer used to stop idle by passing it as false. Since default value is true, it will always restart.
   */
  resetSessionTimeout(restartTimer = true): void {
    if (restartTimer && isPresent(this.userData)) {
      this.popoverController.dismiss(true, '', 'session-timeout');
      const idleTime = this.sessionTimeout * 60 - this.warningTimeout;
      this.setidleTimeStamp();
      this.watchIdle(idleTime, this.warningTimeout);
    } else {
      this.keepalive.stop();
      this.idle.stop();
    }
  }
  /**
   * watchIdle
   * @param idleTime number
   * @param timeOut number
   */
  watchIdle(idleTime: number, timeOut: number): void {
    idleTime = idleTime > Constants.defaultTimerValue ? idleTime : Constants.defaultTimerValue;
    timeOut = timeOut > Constants.defaultTimerValue ? timeOut : Constants.defaultTimerValue;
    if (this.userData) {
      this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);
      this.idle.setIdle(idleTime);
      this.idle.setTimeout(timeOut);
      this.idle.watch();
    } else {
      this.resetSessionTimeout(false);
    }
  }
  setidleTimeStamp() {
    this.userLastActivityTimeStamp = Math.round((new Date() as any) / 1000);
  }
  pause(): void {
    this.isAppMinimize = true;
    if (this.userData) {
      if (this.browser) {
        this.executeSaveAsDraft(-1);
      }
      this.appMinimized.next(true);
    }
    this.resetSessionTimeout(false);
  }
  resume(): void {
    this.isAppMinimize = false;

    if (!this.videoCall && !this.isIOSVideoCallConnected && !this.userData?.appLessSession) {
      // Check video call on going or not and not an app-less session
      if (this.userData) {
        const appResumeTimeStamp = Math.round((new Date() as any) / 1000);
        const sessionRemaining = this.sessionTimeout * 60 - (appResumeTimeStamp - this.userLastActivityTimeStamp);
        if (sessionRemaining > 0) {
          const idle = sessionRemaining - this.warningTimeout;
          const timeOut = sessionRemaining < this.warningTimeout ? sessionRemaining - 1 : this.warningTimeout;
          this.watchIdle(idle, timeOut);
          this.idle.clearInterrupts();
          this.appMinimized.next(false);
        } else {
          this.common.dismissPopover();
          this.setSessionTimeoutCommonCode();
        }
      } else {
        this.common.dismissPopover();
      }
    }
    this.clearAllDeliveredNotifications();
  }
  showSessionTimeoutModal(): void {
    this.sessionModal = true;
    this.popoverController
      .create({
        component: SessionTimeoutComponent,
        componentProps: {},
        id: 'session-timeout',
        cssClass: 'session-timeout-popup',
        size: 'cover',
        backdropDismiss: false,
        mode: 'ios'
      })
      .then((modal: any) => {
        modal.present();
        modal.onDidDismiss().then(({ data }) => {
          this.sessionModal = false;
          if (data) {
            this.trackActivity({
              type: Activity.userAccess,
              name: Activity.sessionRestored,
              des: {
                data: { username: this.userData?.userName },
                desConstant: Activity.sessionRestoredDes
              }
            });
            this.resetSessionTimeout();
          } else if (data === false) {
            this.setSessionTimeoutCommonCode();
          }
        });
      });
  }
  clearAllDeliveredNotifications(): void {
    if (this.platform.is('capacitor')) {
      /** Clear notifications from the notification bar */
      PushNotifications.removeAllDeliveredNotifications();
      LocalNotifications.removeAllDeliveredNotifications();
    }
  }

  setSessionTimeoutCommonCode(): void {
    const msg = this.common.getTranslateData('MESSAGES.SESSION_EXPIRED');
    this.trackActivity({
      type: Activity.userAccess,
      name: Activity.sessionTimeout,
      des: {
        data: { userEmail: this.userData?.userName },
        desConstant: Activity.sessionTimeoutDes
      }
    });
    if (this.isUserLoggedIn()) {
      this.logout();
    }
    this.common.showMessage(msg, { id: Constants.sessionTimeout, autoDismissTimeOut: 3000 });
  }
  setSessionTimeoutTime(): void {
    this.sessionTimeout = isNumberCheck(this.getConfigValue(Config.sessionTimeout))
      ? Number(this.getConfigValue(Config.sessionTimeout))
      : Number(this.localConfig.sessionTimeoutMinutes);
    this.warningTimeout =
      !isBlank(this.userData) && !isBlank(this.getConfigValue(Config.sessionTimeoutWarning))
        ? Number(this.getConfigValue(Config.sessionTimeoutWarning))
        : Number(this.localConfig.sessionTimeoutWarningSeconds);
    this.resetSessionTimeout();
  }
  generateVidyoTocken(params: any): Observable<any> {
    return this.httpService.doPostByUrl({
      payload: params,
      apiUrl: `${environment.pushServerAPIUrl}${Urls.generateVidyoTocken}`
    });
  }

  getUserTags(searchKeyword: string, groupId: number): Observable<UserTag[]> {
    const url = APIs.getUserTags;
    const extraParams = groupId === Constants.patientGroupId ? { messageUserTag: Constants.configTrue } : {};
    const body = {
      ...extraParams,
      group: groupId, // Patient GroupId.
      enroll: Constants.patientEnroll,
      searchKeyword,
      bundleIdentifier: this.localConfig.bundleIdentifier
    };

    return this.httpService.doPost({
      endpoint: url,
      payload: {},
      extraParams: body
    });
  }


  mergeSelectedData(partialData: any, fullData: any): any {
    const mergedOptions: any = JSON.parse(JSON.stringify(fullData));
    partialData.forEach((element) => {
      const index = mergedOptions.findIndex((x) => x.id === element.id);
      if (index >= 0) {
        mergedOptions[index].selected = true;
      }
    });
    return mergedOptions;
  }

  sentPushNotification(
    chatRoomOrToId: any,
    userId: string,
    message: string,
    privilegeKey: string,
    pushDeepLink: any,
    showDoubleVerificationStatus: any,
    notificationData: { sourceId?: string; sourceCategoryId?: string } = {}
  ): any {
    const data: any = {
      chatRoomOrToId,
      userId,
      message,
      environment: environment.alias,
      pushNotificationUpdated: true,
      senderId: this.userData.userId
    };
    if (privilegeKey) {
      data.privilegeKey = privilegeKey;
    }
    if (pushDeepLink) {
      data.pushDeepLink = pushDeepLink;
    }
    if (notificationData) {
      if (notificationData.sourceId && notificationData.sourceId !== undefined && notificationData.sourceId !== '')
        data['sourceId'] = notificationData.sourceId;

      if (notificationData.sourceCategoryId && notificationData.sourceCategoryId !== undefined && notificationData.sourceCategoryId !== '')
        data['sourceCategoryId'] = notificationData.sourceCategoryId;
    }

    const emailNotifcationData = {
      chatRoomOrToId,
      userId,
      message,
      senderName: this.userData.displayName,
      tenantId: this.userData.tenantId,
      subject: this.common.getTranslateData('MESSAGES.EMAIL_NOTIFICATION_MESSAGE'),
      roleId: this.userData.group
    };
    let headers = new HttpHeaders();
    // headers = headers.append('Content-Type', 'multipart/form-data');
    headers = headers.append('Content-Type', 'application/x-www-form-urlencoded');
    if (getValueFromSession(Constants.storageKeys.authToken)) {
      headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
    }
    const promise = new Promise((resolve, reject) => {
      const apiURL = APIs.pushNotification;
      const payload = data;
      if (showDoubleVerificationStatus) {
        resolve([]);
        if (Number(this.userData.group) === Constants.patientGroupId) {
          this.socketService.emitEvent('sendEmailNotification', emailNotifcationData);
        }
      } else {
        if (
          this.checkBranchHours(true).isWorkingHours === true ||
          Number(this.userData.group) !== Constants.patientGroupId
        ) {
          this.httpService
            .doPost({
              endpoint: apiURL,
              payload,
              contentType: 'form',
              loader: false
            })
            .subscribe(
              (res) => {
                const result = res;
                resolve([]);
              },
              (msg) => {
                // Error
                reject(msg);
              }
            );
        } else {
          resolve([]);
          if (Number(this.userData.group) === Constants.patientGroupId) {
            this.socketService.emitEvent('sendEmailNotification"', emailNotifcationData);
          }
        }
      }
    });
    return promise;
  }
  setActivityDescription(data: any, des: string): any {
    return des.replace(new RegExp(Constants.validationPattern.activityDesPattern, 'g'), (key, value) => {
      return data[value];
    });
  }

  checkBranchHours(ignoreBranchHour: boolean): any {
    const response: any = {
      isWorkingHours: false
    };
    const requiredConfig: any = {
      // Multi-site OFF
      workingHour: this.getConfigValue(Config.workingHour),
      branchWorkingDay: this.getConfigValue(Config.branchWorkingDays) || '1,2,3,4,5,6,0',
      chatStartTime: this.getConfigValue(Config.homeInfusionStartTime) || '9:0',
      chatEndTime: this.getConfigValue(Config.homeInfusionEndTime) || '6:0'
    };
    if (!ignoreBranchHour) {
      requiredConfig.branchStartTime = this.getConfigValue(Config.branchStartTime) || '9:0';
      requiredConfig.branchEndTime = this.getConfigValue(Config.branchEndTime) || '6:0';
    }
    if (this.isEnableConfig(Config.enableMultiSite)) {
      requiredConfig.branchWorkingDay = this.getSiteConfigValue(Config.branchWorkingDays) || '1,2,3,4,5,6,0';
      requiredConfig.chatStartTime = this.getSiteConfigValue(Config.chatStartTime) || '9:0';
      requiredConfig.chatEndTime = this.getSiteConfigValue(Config.chatEndTime) || '6:0';
      requiredConfig.workingHour = this.getSiteConfigValue(Config.workingHour);
      if (!ignoreBranchHour) {
        requiredConfig.branchStartTime = this.getSiteConfigValue(Config.branchStartTime) || '9:0';
        requiredConfig.branchEndTime = this.getSiteConfigValue(Config.branchEndTime) || '6:0';
      }
    }
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    let homeInfusionStartTime = requiredConfig.chatStartTime || '9:0';
    let homeInfusionEndTime = requiredConfig.chatEndTime || '6:0';
    let branchStartTime = !ignoreBranchHour ? requiredConfig.branchStartTime : '9:0';
    let branchEndTime = !ignoreBranchHour ? requiredConfig.branchEndTime : '6:0';
    homeInfusionStartTime = clientToGmtTime(homeInfusionStartTime, true, this.timezone, '', '');
    homeInfusionEndTime = clientToGmtTime(homeInfusionEndTime, true, this.timezone, '', '');
    if (!ignoreBranchHour) {
      branchStartTime = clientToGmtTime(branchStartTime, true, this.timezone, '', '');
      branchEndTime = clientToGmtTime(branchEndTime, true, this.timezone, '', '');
    }
    let use24HourEnabledBranchHours = false;
    if (+requiredConfig.workingHour) {
      if (!ignoreBranchHour) {
        use24HourEnabledBranchHours = true;
      } else {
        response.isWorkingHours = true;
        return response;
      }
    }
    if (use24HourEnabledBranchHours) {
      homeInfusionStartTime = branchStartTime;
      homeInfusionEndTime = branchEndTime;
    }
    if (homeInfusionStartTime.indexOf(':') !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(':')[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(':')[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(':') !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(':')[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(':')[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }

    if (requiredConfig.branchWorkingDay.indexOf(currentDay) !== -1) {
      if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
        if (
          currentHours > response.homeInfusionStartTimeHours ||
          (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes) ||
          currentHours < response.homeInfusionEndTimeHours ||
          (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes)
        ) {
          response.isWorkingHours = true;
        }
      } else if (
        (currentHours > response.homeInfusionStartTimeHours ||
          (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
        (currentHours < response.homeInfusionEndTimeHours ||
          (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))
      ) {
        response.isWorkingHours = true;
      }
    }
    return response;
  }
  createPaletteButton(params: any, imageWidth?: number): void {
    let valueAddedCondition = true;
    let fieldMandatory = true;
    let pendingApprove = false;
    const userId = params.documentSignersDetails ? params.documentSignersDetails.id : null;
    const pageCount = params.pageCount;
    const fitLeft = Math.abs(params.orginalFileSizeInfo.left);
    const myLocation = {
      fixTop: `${parseInt(params.paletteSize.top)}${Constants.pixel}`,
      fixLeft: `${parseInt(params.paletteSize.left)}${Constants.pixel}`,
      top: `${parseInt(params.paletteSize.top)}${Constants.pixel}`,
      left: `${parseInt(params.paletteSize.left)}${Constants.pixel}`,
      fitLeft,
      fitTop: params.orginalFileSizeInfo.top,
      width: `${parseInt(params.paletteSize.width)}${Constants.pixel}`,
      height: `${parseInt(params.paletteSize.height)}${Constants.pixel}`,
      fitWidth: parseInt(params.orginalFileSizeInfo.width),
      fitHeight: parseInt(params.orginalFileSizeInfo.height),
      img_height: `${parseInt(params.paletteSize.height) - 2}${Constants.pixel}`,
      img_width: `${parseInt(params.paletteSize.width) - 2}${Constants.pixel}`,
      checkboxLabelWidth: '',
      checkboxLabelHeight: '',
      fontSize: this.getFontSize(params.paletteSize.height, imageWidth)
    };
    let borderColor = Signature.backgroundColor.otherBorderDottedColor;
    let buttonColor = Signature.backgroundColor.otherBorderColor;
    if (userId === this.userData.userId) {
      borderColor = Signature.backgroundColor.meBorderDottedColor;
      buttonColor = Signature.backgroundColor.meBorderColor;
    } else if (
      (params.signatureTypeTagData.allowAssociateRoles && userId === params.data.associate.id) ||
      (params.signatureTypeTagData.obtainSignature &&
        !params.signatureTypeTagData.allowAssociateRoles &&
        params.signatureTypeTagData.allowRecipientRoles &&
        userId === 0) ||
      (params.signatureTypeTagData.obtainSignature &&
        !params.signatureTypeTagData.allowAssociateRoles &&
        !params.signatureTypeTagData.allowRecipientRoles &&
        userId === 0)
    ) {
      borderColor = Signature.backgroundColor.associateBorderDottedColor;
      buttonColor = Signature.backgroundColor.associateBorderColor;
    }
    if (params.signatureTypeTagData.allowPendingApproveSignature && params.documentSignersDetails.pendingApproveUser) {
      pendingApprove = true;
    }

    const data = {
      id: uniqueId(2),
      view: false,
      userId,
      mandatory: fieldMandatory,
      myLocation,
      group: false,
      groupColor: '',
      groupName: '',
      signature: false,
      pendingApproveUser: pendingApprove,
      name: '',
      signpad: true,
      nickName: '',
      checkbox: false,
      checkboxLabel: false,
      checkboxLabelValue: '',
      isResizable: true,
      isDraggable: true,
      fieldorder: 0,
      page: pageCount,
      hidePalette: false,
      borderColor,
      iconColor: buttonColor,
      resizeMinWidth: ConfigValues.messages.configurations.signatureDocumentPalette.minWidth,
      resizeMinHeight: ConfigValues.messages.configurations.signatureDocumentPalette.minHeight,
      resizeMaxWidth: '',
      resizeMaxHeight: 0
    };
    if (params.groupObject) {
      if (!isBlank(params.groupObject.groupName)) {
        data.groupName = params.groupObject.groupName;
        data.group = true;
      }
    } else if (params.groupObj) {
      if (!isBlank(params.groupObj.groupName)) {
        data.groupName = params.groupObj.groupName;
        data.group = true;
      }
    }
    if (params.field === Signature.paletteType.textField) {
      data.name = Signature.paletteType.textValue;
      data.signpad = false;
      data.nickName = Signature.paletteType.textValue;
      data.resizeMaxWidth = Signature.paletteSize.maxWidth;
      data.resizeMaxHeight = ConfigValues.messages.configurations.signatureDocumentPalette.resizeMaxHeightForText;
      if (params.signatureTypeTagData.textBoxAreMandatory === Signature.optionalValue) {
        fieldMandatory = false;
        data.mandatory = fieldMandatory;
        params.addObjects.push(data);
      } else if (params.signatureTypeTagData.textBoxAreMandatory === Signature.senderChoice) {
        this.showValidationMessageForUserChoice((confirmVal: any) => {
          data.mandatory = confirmVal;
          params.addObjects.push(data);
        }, Signature.paletteType.textField);
      } else {
        params.addObjects.push(data);
      }
    } else if (params.field === 'sign') {
      data.name = Signature.paletteType.signValue;
      data.signpad = true;
      data.nickName = Signature.paletteType.signValue;
      data.resizeMaxWidth = String(ConfigValues.messages.configurations.signatureDocumentPalette.signwidth);
      data.resizeMaxHeight = ConfigValues.messages.configurations.signatureDocumentPalette.signheight;

      if (params.signatureTypeTagData.signatureAreMandatory === Signature.optionalValue) {
        fieldMandatory = false;
        data.mandatory = fieldMandatory;
        params.addObjects.push(data);
      } else if (params.signatureTypeTagData.signatureAreMandatory === Signature.senderChoice) {
        this.showValidationMessageForUserChoice((confirmVal: any) => {
          data.mandatory = confirmVal;
          params.addObjects.push(data);
        }, Signature.paletteType.signValue.toLowerCase());
      } else {
        params.addObjects.push(data);
      }
    } else if (params.field === Signature.paletteType.checkboxField) {
      if (params.addObjects.length) {
        let checkboxAllowed = false;
        params.addObjects.forEach((item) => {
          if (item.name === Signature.paletteType.checkboxValue) {
            checkboxAllowed = true;
          }
        });
        if (checkboxAllowed && !params.signatureTypeTagData.multipleCheckBoxAllowed) {
          valueAddedCondition = false;
          this.common.showMessage(this.common.getTranslateData('ERROR_MESSAGES.CHECKBOX_NOT_ALLOWED'));
        }
      }

      if (valueAddedCondition) {
        data.name = Signature.paletteType.checkboxValue;
        data.signpad = false;
        data.checkbox = true;
        data.checkboxLabel = params.checkboxLabelCondition;
        let responsiveCheckBox;
        if (window.innerWidth < 481) {
          responsiveCheckBox = 32;
        } else {
          responsiveCheckBox = 30;
        }

        data.myLocation.height =
          ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight + Constants.pixel;
        if (!params.checkboxLabelCondition) {
          data.myLocation.width =
            ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight -
            responsiveCheckBox +
            Constants.pixel;
          data.myLocation.height =
            ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight -
            responsiveCheckBox +
            Constants.pixel;
          data.isResizable = false;
        } else {
          data.myLocation.checkboxLabelWidth =
            ConfigValues.messages.configurations.signatureDocumentPalette.signwidth -
            ConfigValues.messages.configurations.signatureDocumentPalette.checkBoxLabelWithRedeuse +
            Constants.pixel;
          data.myLocation.checkboxLabelHeight =
            ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight -
            ConfigValues.messages.configurations.signatureDocumentPalette.checkBoxLabelHeightRedeuse +
            Constants.pixel;

          data.resizeMaxWidth = Signature.paletteSize.maxWidth;
          data.resizeMaxHeight = ConfigValues.messages.configurations.signatureDocumentPalette.resizeMaxHeightForText;
          data.resizeMinHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight + 5;
        }

        // TODO: Need to add some conditions.
        if (
          params.signatureTypeTagData.checkBoxAreMandatory === Signature.optionalValue &&
          isBlank(params.groupObject || params.groupObj)
        ) {
          if (
            params.field === Signature.paletteType.checkboxField &&
            params.signatureTypeTagData.checkBoxAreMandatory === Signature.senderChoice
          ) {
            this.showValidationMessageForUserChoice((confirmVal: any) => {
              data.mandatory = confirmVal;
              params.addObjects.push(data);
              valueAddedCondition = false;
            }, Signature.paletteType.checkboxField);
          } else {
            data.mandatory = false;
            params.addObjects.push(data);
            valueAddedCondition = false;
          }
        } else if (
          params.signatureTypeTagData.checkBoxGroupAllowed &&
          ((isPresent(params.groupObject) && isPresent(params.groupObject.groupName)) ||
            (isPresent(params.groupObj) && isPresent(params.groupObj.groupName)))
        ) {
          if (
            params.signatureTypeTagData.checkBoxGroupAreMandatory ===
            Signature.checkboxGroupValidationBehavior.allRequiredValue
          ) {
            data.mandatory = true;
          } else {
            data.mandatory = false;
          }
          params.addObjects.push(data);
          valueAddedCondition = false;
        } else {
          if (
            params.field === Signature.paletteType.checkboxField &&
            params.signatureTypeTagData.checkBoxAreMandatory === Signature.senderChoice
          ) {
            this.showValidationMessageForUserChoice((confirmVal: any) => {
              data.mandatory = confirmVal;
              params.addObjects.push(data);
            }, Signature.paletteType.checkboxField);
          } else {
            params.addObjects.push(data);
          }
        }
      }
    }
    let activityDescriptionfields: string;
    if (data.name === Signature.paletteType.signValue) {
      activityDescriptionfields = this.common.getTranslateDataWithParam('MESSAGES.ADDED_FIELD', {
        fieldName: `${Signature.paletteType.signatureField}`,
        page: `${data.page}`
      });
    } else if (data.name === Signature.paletteType.textValue) {
      activityDescriptionfields = this.common.getTranslateDataWithParam('MESSAGES.ADDED_FIELD', {
        fieldName: `${Signature.paletteType.textField}`,
        page: `${data.page}`
      });
    } else {
      const checkBoxCondition = data.mandatory ? Signature.mandatory : Signature.notMandatory;
      activityDescriptionfields = this.common.getTranslateDataWithParam('MESSAGES.ADDED_CHECKBOX', {
        checkBoxCondition: `${checkBoxCondition}`,
        page: `${data.page}`
      });
    }
    const addFiledDes = {
      displayName: this.userData.displayName,
      activityDescriptionfields,
      actualFileName: this.docDetails.actualFileName,
      documentUniqueName: this.docDetails.documentUniqueName
    };
    this.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.addField,
      des: { data: addFiledDes, desConstant: Activity.addFieldDes },
      linkageId: this.docDetails.actualFileName
    });
  }
  getDocumentWidthAndHeight(
    left: number,
    top: number,
    width: number,
    height: number,
    reverse: boolean,
    imageInfo: any,
    target: any,
    signDocument: any
  ): any {
    top = top < 0 ? 2 : top;
    let orginalWidth = 800;
    let orginalHeight = 1100;
    let ratioWidth, ratioHeight;
    let getFitTop, getFitWidth, getFitHeight, getFitLeft;
    const getImageInfo = target;
    const getWrapperImageInfo = signDocument?.nativeElement?.offsetWidth - 3;
    if (getImageInfo) {
      if (getImageInfo.naturalWidth) {
        orginalWidth = getImageInfo.naturalWidth;
        orginalHeight = getImageInfo.naturalHeight;
      }

      const actualWidth = getImageInfo.offsetWidth;
      const actualHeight = getImageInfo.offsetHeight;

      if (reverse) {
        ratioWidth = actualWidth / orginalWidth;
        ratioHeight = actualHeight / orginalHeight;
        getFitLeft = Math.abs(ratioWidth * left + (getWrapperImageInfo - actualWidth) / 2);
      } else {
        ratioWidth = orginalWidth / actualWidth;
        ratioHeight = orginalHeight / actualHeight;
        getFitLeft = ratioWidth * (left - (getWrapperImageInfo - actualWidth) / 2);
      }
      getFitTop = ratioHeight * top;
      getFitWidth = ratioWidth * width;
      getFitHeight = ratioHeight * height;
    }
    return {
      left: getFitLeft,
      top: getFitTop,
      width: getFitWidth,
      height: getFitHeight
    };
  }
  showWarningMessages(patientIdentity: string, staffId: string, pageType: string): any {
    let msgType: string;
    if (isBlank(patientIdentity) && isBlank(staffId)) {
      msgType = Constants.staffIdMRNMissing;
    } else if (isBlank(patientIdentity)) {
      msgType = Constants.patientMRNMissing;
    } else if (isBlank(staffId)) {
      msgType = Constants.staffIdMissing;
    }
    return this.setMsgTitle(msgType, pageType);
  }
  setMsgTitle(msgType: string, pageType: string): any {
    let msg: string;
    let title: string;
    let buttons = [];
    switch (msgType) {
      case Constants.staffIdMRNMissing:
        msg = 'MESSAGES.STAFF_MRN_MISSING';
        title = 'MESSAGES.STAFF_MRN_MISSING_TITLE';
        break;
      case Constants.patientMRNMissing:
        msg = 'MESSAGES.PATIENT_MRN_MISSING';
        title = 'MESSAGES.PATIENT_MRN_MISSING_TITLE';
        break;
      case Constants.staffIdMissing:
        msg = 'MESSAGES.STAFF_ID_MISSING';
        title = 'MESSAGES.STAFF_ID_MISSING_TITLE';
        break;
      default:
        break;
    }
    if (pageType === Constants.pageType.recipients && isBlank(msg)) {
      msg = 'MESSAGES.SINGLE_RECIPIENT';
      title = 'MESSAGES.ARE_YOU_SURE';
    }
    if (msgType === Constants.staffIdMRNMissing || msgType === Constants.patientMRNMissing || msgType === Constants.staffIdMissing) {
      buttons = [
        { text: 'BUTTONS.CONTINUE_ANYWAY', confirm: true, id: 'continue' },
        { text: 'BUTTONS.GO_BACK', confirm: false, id: 'go-back' }
      ];
    }
    return { msg, title, buttons };
  }

  checkAllowEditForm(data: any, activityData: any): Promise<any> {
    return new Promise((resolve) => {
      const payload = {
        formid: data.formId,
        patientid: data.patientId,
        admissionId: data.admissionId
      };
      const isPatientName = isBlank(data.patientName);
      this.httpService.doPost({ endpoint: APIs.checkAllowEditForm, payload }).subscribe((editStatus: any) => {
        if (editStatus.ALLOWEDIT && Number(editStatus.FROMID) === Number(this.userData.userId)) {
          // Go to Pending
          const buttons = [
            {
              text: this.common.getTranslateData('BUTTONS.CONTINUE'),
              confirm: false
            },
            {
              text: this.common.getTranslateData('BUTTONS.GO_TO_PENDING'),
              confirm: true
            }
          ];
          const alertData = {
            message: isPatientName
              ? this.common.getTranslateDataWithParam('MESSAGES.PENDING_FORM_EXISTS', { formName: data.name })
              : this.common.getTranslateDataWithParam('MESSAGES.PENDING_FORM_EXISTS_PATIENT', {
                formName: data.formName,
                patientName: data.patientName
              }),
            header: 'MESSAGES.ARE_YOU_SURE',
            buttons
          };
          this.common.showAlert(alertData).then((confirmation) => {
            if (confirmation) {
              this.trackActivity({
                type: Activity.forms,
                name: isPatientName ? Activity.patientFormAlreadyPending : Activity.formAlreadyPending,
                des: {
                  data: activityData,
                  desConstant: isPatientName ? Activity.patientFormAlreadyPendingDes : Activity.formAlreadyPendingDes
                }
              });
              this.common.redirectToPage('form-center/pending-forms');
            } else {
              this.trackActivity({
                type: Activity.forms,
                name: isPatientName ? Activity.patientFormAlreadyPendingContinue : Activity.formAlreadyPendingContinue,
                des: {
                  data: activityData,
                  desConstant: isPatientName
                    ? Activity.patientFormAlreadyPendingContinueDes
                    : Activity.formAlreadyPendingContinueDes
                }
              });
              this.checkDraftForm(data, activityData).then((res: any) => {
                resolve(res);
              });
            }
          });
        } else {
          this.checkDraftForm(data, activityData).then((res: any) => {
            resolve(res);
          });
        }
      });
    });
  }
  cancelDocumentOrForm(type: 'DOCUMENTS' | 'FORMS', documentName: string, tab: string = 'PENDING'): Promise<string> {
    const {
      'MESSAGES.ARE_YOU_SURE': title,
      'LABELS.CANCEL_ALERT_WITH_REASON': inputPlaceholder,
      'TITLES.SIGNATURE_REQUEST': signatureRequest,
      'LABELS.WORK_LIST_TAB': worklistTab,
      'LABELS.FORM': form,
      'BUTTONS.CANCEL': cancelButtonText,
      'BUTTONS.CONTINUE': confirmButtonText
    } = this.common.getTranslateData([
      'MESSAGES.ARE_YOU_SURE',
      'LABELS.CANCEL_ALERT_WITH_REASON',
      'TITLES.SIGNATURE_REQUEST',
      'LABELS.WORK_LIST_TAB',
      'LABELS.FORM',
      'BUTTONS.CANCEL',
      'BUTTONS.CONTINUE'
    ]);
    const reasonForCancellation = 'INPUT.REASON_FOR_CANCEL';
    const reasonAlertData = {
      message: this.common.getTranslateDataWithParam('MESSAGES.CANCEL_FORM_OR_DOCUMENT_CONFIRM', {
        type: type === 'DOCUMENTS' ? signatureRequest : form,
        documentName,
        tab: isPresent(worklistTab) && Object.keys(worklistTab).includes(tab) ? worklistTab[tab] : ''
      }),
      header: title,
      cssClass: 'common-alert cancel-reason-alert',
      inputs: [
        {
          name: reasonForCancellation,
          type: 'text',
          attributes: {
            required: true
          },
          placeholder: `${inputPlaceholder}*`
        }
      ],
      buttons: [
        {
          text: cancelButtonText,
          role: 'cancel',
          cssClass: 'warn-btn alert-cancel'
        },
        {
          text: confirmButtonText,
          cssClass: 'warn-btn alert-ok',
          id: 'reason-confirm',
          requiredInputs: [reasonForCancellation]
        }
      ],
      alertId: 'cancel-reason-alert'
    };
    return new Promise((resolve) => {
      // Later when you want to remove the listener
      this.common.showAlert(reasonAlertData).then((response) => {
        if (response) {
          const label = type === 'DOCUMENTS' ? 'MESSAGES.CONFIRM_TO_CANCEL_DOCUMENT' : 'MESSAGES.CONFIRM_TO_CANCEL_FORM';
          const alertData = {
            message: label,
            header: 'MESSAGES.ARE_YOU_SURE'
          };
          this.common.showAlert(alertData).then((confirm) => {
            resolve(confirm ? response[reasonForCancellation] : '');
          });
        } else {
          resolve('');
        }
      });
    });
  }
  checkDraftForm(data: any, activityData: any): Promise<any> {
    return new Promise((resolve) => {
      const payload = {
        formid: data.formId,
        patientid: data.combinedId ? data.combinedId : data.patientId,
        admissionId: data.admissionId
      };
      this.httpService
        .doPost({ endpoint: APIs.checkDraftForm, payload, loader: false })
        .subscribe((draftStatus: any) => {
          this.isLoading = false;
          if (!draftStatus.nodrafts && !this.automaticLinkedItems) {
            const buttons = [
              {
                text: this.common.getTranslateData('BUTTONS.CONTINUE'),
                confirm: false
              },
              {
                text: this.common.getTranslateData('BUTTONS.GO_TO_DRAFTS'),
                confirm: true
              }
            ];
            let draftMessage;
            if (!data.patientAssociation) {
              draftMessage = this.common.getTranslateDataWithParam('MESSAGES.DRAFT_EXISTS_FOR_USER', {
                formName: data.formName,
                patientName: data.patientName
              });
            } else {
              draftMessage = this.common.getTranslateDataWithParam('MESSAGES.DRAFT_FORM_EXISTS_PATIENT', {
                formName: data.formName,
                patientName: data.patientName
              });
            }
            if (draftMessage) {
              const alertData = {
                message: draftMessage,
                header: 'MESSAGES.ARE_YOU_SURE',
                buttons
              };
              this.common.showAlert(alertData).then((confirmation) => {
                if (confirmation) {
                  this.trackActivity({
                    type: Activity.forms,
                    name: Activity.patientFormAlreadyDraft,
                    des: {
                      data: activityData,
                      desConstant: Activity.patientFormAlreadyDraftDes
                    }
                  });
                  this.common.redirectToPage('form-center/draft-forms');
                } else {
                  this.trackActivity({
                    type: Activity.forms,
                    name: Activity.patientFormAlreadyDraftContinue,
                    des: {
                      data: activityData,
                      desConstant: Activity.patientFormAlreadyDraftContinueDes
                    }
                  });
                  resolve({ status: true });
                }
              });
            }
          } else {
            resolve({ status: true });
          }
        });
    });
  }
  sendFormToRecipients(data: any): Observable<any> {
    const extraParams = {
      bundleIdentifier: this.localConfig.bundleIdentifier
    };
    let endpoint = APIs.sendFormToRecipients;
    if (data.resend) {
      endpoint = APIs.resendFormToRecipients;
      data.resend = undefined;
      delete data.resend;
    }
    return this.httpService.doPost({
      endpoint,
      payload: JSON.stringify(data),
      contentType: 'form',
      extraParams,
      loader: true
    });
  }
  formPolling(response: any): void {
    const selectedRecipientsPollingWithUser = [];
    let userDetails = {};
    if (response.send_to) {
      response.send_to.forEach((value) => {
        userDetails = {
          userid: value,
          senderId: this.userData.userId,
          organizationMasterId: this.userData.organizationMasterId,
          formSendMode: response.formSendMode,
          applessMode: response.applessMode,
          sentId: response.sentId
        };
        selectedRecipientsPollingWithUser.push(userDetails);
      });
    }
    const sendUserData = {
      username: this.userData.userName,
      displayname: this.userData.displayName,
      userid: this.userData.userId,
      roleId: this.userData.roleId,
      mobile: this.userData.mobile,
      countryCode: this.userData.countryCode
    };
    selectedRecipientsPollingWithUser.push(sendUserData);
    this.socketService.emitEvent(Socket.sendFormsToRecipients, selectedRecipientsPollingWithUser);
  }
  setDocFilename(fileNameDetails: any): any {
    const date = formatDate();
    const today = `${formatDate(date, Constants.dateFormat.mmddyy)}`;
    const docName = fileNameDetails.resultDocument.displayText.text;
    const docNameArray = docName.split('-');
    if (docNameArray.length < 2) {
      docNameArray.unshift(fileNameDetails.typeName);
    }
    const replaceDocumentName =
      fileNameDetails.resultDocument.displayText.document &&
        !isBlank(fileNameDetails.resultDocument.displayText.document)
        ? fileNameDetails.resultDocument.displayText.document.replace(/\.[^/.]+$/, "")
        : docNameArray[1];
    const metaTagFileNameFormatText = fileNameDetails.resultDocument.type.fileSavingFormatText;
    let fileTargetNameUpdated = metaTagFileNameFormatText
      .replace('{tagName}', fileNameDetails.typeName)
      .replace('{documentName}', replaceDocumentName)
      .replace('{date}', today)
      .replace('{firstName}', fileNameDetails.associatePatient.firstName)
      .replace('{lastName}', fileNameDetails.associatePatient.lastName)
      .replace('{ESI}', fileNameDetails.esi);
    fileTargetNameUpdated = `${fileTargetNameUpdated}-${new Date().getTime()}.${Constants.documentTypes.pdf}`;
    fileTargetNameUpdated = fileTargetNameUpdated.replace('--', '-');
    fileTargetNameUpdated = fileTargetNameUpdated.replace('-.', '.');
    fileTargetNameUpdated = fileTargetNameUpdated.replace(/^\-/, '');
    fileTargetNameUpdated = fileTargetNameUpdated.replace(/-\s*$/, '');
    return fileTargetNameUpdated;
  }
  fetchVisitUserResourceData(reqBody?: any): Observable<any> {
    const url = APIs.visitUserResourceData;
    const body = {
      type: (reqBody && reqBody.type) || 'staffs',
      tenantId: !isBlank(this.userData) ? this.userData?.tenantId : '',
      pageCount: (reqBody && reqBody.pageCount) || 0,
      searchKeyword: (reqBody && reqBody.searchKeyword) || '',
      offset: (reqBody && reqBody.offset) || 0,
      limit: (reqBody && reqBody.limit) || 20
    };
    return this.httpService.doPost({
      endpoint: url,
      payload: body,
      contentType: 'form',
      parseToString: true
    });
  }
  fetchAvailability(reqBody?: any): Observable<any> {
    const url = APIs.getAvailability;
    let body: any = {
      isNew: reqBody && reqBody.isNew,
      page: reqBody && reqBody.page,
      pageLoad: reqBody && reqBody.pageLoad,
      selectedFilterType: reqBody && reqBody.selectedFilterType,
      selectedUserId: reqBody && reqBody.userId,
      tenantTimeZoneName: reqBody && reqBody.tenantTimeZoneName,
      startIntervalTime: reqBody && reqBody.startIntervalTime,
      endIntervalTime: reqBody && reqBody.endIntervalTime
    };
    if (reqBody.statusFilter) {
      body = { ...body, statusFilter: reqBody.statusFilter };
    }
    if (reqBody.siteIds) {
      body = { ...body, siteIds: reqBody.siteIds };
    }
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }

  getVisit(reqBody?: any): Observable<any> {
    const url = APIs.getVisit;
    let body: any = {
      calendarType: reqBody && reqBody.calendarType,
      tenantTimeZoneName: reqBody && reqBody.tenantTimeZoneName,
      startIntervalTime: reqBody && reqBody.startIntervalTime,
      endIntervalTime: reqBody && reqBody.endIntervalTime
    };
    if (reqBody.pageLoad) {
      body = {
        ...body,
        pageLoad: reqBody.pageLoad
      };
    }
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  getVisitById(reqBody?: any): Observable<any> {
    const url = APIs.getVisitById;
    const body = {
      id: reqBody && reqBody.id,
      tenantTimeZoneName: reqBody && reqBody.tenantTimeZoneName
    };
    return this.httpService
      .doPost({
        endpoint: url,
        payload: JSON.stringify(body),
        contentType: 'form',
        parseToString: false
      })
      .pipe(
        map(
          (x: any) =>
            new ScheduleVisitSchemas({
              ...x.items[0],
              operation: Constants.fetch
            })
        )
      );
  }
  updateVisitSchedule(reqBody: any): Observable<any> {
    const url = APIs.updateVisitSchedule;
    const body = new ScheduleVisitSchemas({
      ...reqBody,
      operation: Constants.update
    });
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  confirmWithoutContact(isEdit = false): Promise<boolean> {
    const buttons = [
      {
        text: 'BUTTONS.RETURN_TO_EDIT',
        role: 'cancel',
        cssClass: 'warn-btn alert-cencel full-width',
        handler: false
      },
      {
        text: 'BUTTONS.CONTINUE_WITHOUT_CONTACT',
        role: 'withoutContact',
        cssClass: 'warn-btn alert-ok full-width',
        handler: true
      }
    ];
    let alertMessage = this.common.getTranslateData('MESSAGES.CONFIRM_WITHOUT_CONTACT');
    if(isEdit){
      alertMessage = this.common.getTranslateData('MESSAGES.CONFIRM_WITHOUT_CONTACT_FOR_UPDATE');
    }
    const alertData = {
      message: alertMessage,
      header: this.common.getTranslateData('MESSAGES.ARE_YOU_SURE'),
      buttons,
      mode: 'md'
    };
    return new Promise((resolve) => {
      this.common.showCustomAlert(alertData).then((confirmation) => {
        resolve(confirmation);
      });
    });
  }
  getMessageDeliveredUsers(params: { messageId: string; deliveryStatus: MessageDeliveryStatus; showLoader?: boolean }): Observable<{
    deliveredUsers?: MessageDeliveredUsers[];
    readUsers: MessageDeliveredUsers[];
  }> {
    const url = APIs.deliveryStatusFetch
      .replace(/{messageId}/g, params.messageId.toString())
      .replace(/{deliveryStatus}/g, params.deliveryStatus.toString());
    return this.httpService
      .doGet({
        endpoint: url,
        loader: params.showLoader ?? true
      })
      .pipe(
        map((result: DeliveredUsers[]) => {
          const deliveredUsers = result.map((el) => ({
            avatar: el.avatar,
            userType: el.userType,
            deliveryTime: el.deliverytime,
            displayName: el.displayName,
            readTime: el.read_time,
            lastactivity: +el.read_time,
            userid: el.userid
          }));
          const readUsers = deliveredUsers.filter((el) => el.readTime);
          return { deliveredUsers, readUsers };
        })
      );
  }

  getAllVisits(reqBody?: any): Observable<any> {
    const url = APIs.getVisit;
    const body = {
      calendarType: reqBody && reqBody.calendarType,
      selectedUserId: reqBody && reqBody.selectedUserId,
      isNew: reqBody && reqBody.isNew,
      pageLoad: reqBody && reqBody.pageLoad,
      selectedFilterType: reqBody && reqBody.selectedFilterType,
      startIntervalTime: reqBody && reqBody.startIntervalTime,
      endIntervalTime: reqBody && reqBody.endIntervalTime,
      statusFilter: reqBody && reqBody.statusFilter,
      selectedVisitLocations: reqBody && reqBody.selectedVisitLocations,
      tenantTimeZoneName: reqBody && reqBody.tenantTimeZoneName
    };
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }

  getvisitLocations(reqBody?: any) {
    const url = APIs.getVisitLocationType;
    const body = {
      isFrom: reqBody
    };
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  getLocationTypes() {
    const url = APIs.getLocationTypes;
    return this.httpService.doGet({ endpoint: url, extraParams: {} });
  }
  getLocationNames(reqBody?: any) {
    const url = APIs.locationNames;
    return this.httpService.doGet({ endpoint: url, extraParams: reqBody });
  }
  getPayor(reqBody?: any) {
    const url = APIs.getPayorList;
    return this.httpService.doGet({ endpoint: url, extraParams: reqBody });
  }
  getVisitAvailableUserResource(reqBody?: any) {
    const url = APIs.getAvailableUserResource;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  getVisitAdministrationDetails(reqBody?: any) {
    const url = APIs.getAdministrationDetails;
    return this.httpService.doGet({ endpoint: url, extraParams: reqBody });
  }
  getVisitTaskDetails(reqBody?: any) {
    const url = APIs.getVisitTask;
    return this.httpService.doGet({ endpoint: url, extraParams: reqBody });
  }
  getAllTimeZones(): Observable<TimeZones> {
    if (this.timeZones) {
      return of(this.timeZones);
    }
    const url = APIs.getTIimezones;
    return this.httpService.doGet({ endpoint: url, extraParams: {} });
  }
  getResourcesData(reqBody?: any) {
    const url = APIs.getResource;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  fetchPatientData(reqBody?: any): Observable<any> {
    const url = APIs.visitUserResourceData;
    const body = {
      type: reqBody && reqBody.type,
      tenantId: !isBlank(this.userData) ? this.userData?.tenantId : '',
      pageCount: (reqBody && reqBody.pageCount) || 0,
      searchKeyword: (reqBody && reqBody.searchKeyword) || '',
      offset: (reqBody && reqBody.offset) || -1,
      limit: (reqBody && reqBody.limit) || -1,
      createOwnPrivilege: reqBody && reqBody.createOwnPrivilege
    };
    return this.httpService.doPost({
      endpoint: url,
      payload: body,
      contentType: 'form',
      parseToString: true
    });
  }

  viewAvailability(reqBody?: any): Observable<any> {
    const url = APIs.viewAvailability;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  manageVisit(reqBody?: any): Observable<any> {
    const url = APIs.visitSchedule;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  uploadAttachment(reqBody?: any, fileName?: any): void {
    const extraParams = {
      fileName: fileName
    };
    this.httpService
      .doPost({
        endpoint: APIs.fileAttachment,
        payload: JSON.stringify(reqBody),
        contentType: 'form',
        parseToString: true,
        loader: false,
        extraParams
      })
      .subscribe();
  }
  visitScheduleActivityTrack(reqBody?: any): Observable<any> {
    const url = APIs.visitScheduleActivity;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: body,
      contentType: 'form',
      parseToString: true
    });
  }

  deleteVisit(reqBody: any): Observable<any> {
    const url = APIs.updateVisitSchedule;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false
    });
  }
  /**
   * patientNameDisplay used to format patientName
   * @param patient :any
   * @returns patientName :string
   */
  patientNameDisplay(patient): string {
    let mdlRecDisplayName;
    let patientName = '';
    if (patient.caregiver_userid != null) {
      mdlRecDisplayName = `${patient.displayname} (${patient.caregiver_displayname})`;
      patientName = mdlRecDisplayName;
      if (patient.dob && patient.dob !== '' && patient.dob !== null) {
        mdlRecDisplayName = `${mdlRecDisplayName} -${moment(patient.dob).format(Constants.dateFormat.mdy)}`;
      }
      if (patient.IdentityValue) {
        mdlRecDisplayName = `${mdlRecDisplayName} [MRN:${patient.IdentityValue}]`;
      }
      patientName = mdlRecDisplayName;
    } else {
      mdlRecDisplayName = patient.displayname;
      if (patient.dob && patient.dob !== '' && patient.dob !== null) {
        mdlRecDisplayName = `${mdlRecDisplayName} - ${moment(patient.dob).format(Constants.dateFormat.mdy)}`;
      }
      if (patient.IdentityValue) {
        mdlRecDisplayName = `${mdlRecDisplayName} [MRN:${patient.IdentityValue}]`;
      }
      patientName = mdlRecDisplayName;
    }
    return patientName;
  }
  /**
   * getVisitDetails used to return observable for visit details API response.
   * @param id [string | number]
   * @returns Obeservable
   */
  getVisitDetails(id: string | number): Observable<CreateVisitSchemas> {
    const url = APIs.getVisitDetails;
    return this.httpService
      .doPost({ endpoint: url, payload: { id: id }, contentType: 'form' })
      .pipe(map(({ items }) => new CreateVisitSchemas(items[0])));
  }

  updateToAppLessVideo(data) {
    const url = APIs.updateAppLessVideoStatus;
    return this.httpService.doPost({ endpoint: url, payload: data, contentType: 'form' });
  }

  getViewHistoryDetails(reqBody: any): Observable<any> {
    const url = APIs.getVisitTrackHistory;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form'
    });
  }
  getDocTypeIntegrationStatus(params: any): Observable<any> {
    const url = APIs.checkMessageTagIntegration;
    const body = {
      patient_id: params.patientId,
      message_tag_id: params.messageId,
      admissionId: params?.admissionId || undefined,
      action: params.action
    };
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      contentType: 'form',
      parseToString: false,
      skipErrorHandling: true
    });
  }
  getSites() {
    return this.userData.mySites;
  }

  showValidationMessageForUserChoice(callBack: any, field: string) {
    const buttons = [
      {
        text: this.common.getTranslateData('BUTTONS.NO'),
        confirm: false
      },
      {
        text: this.common.getTranslateData('BUTTONS.YES'),
        confirm: true
      }
    ];
    const alertData = {
      message:
        field === Signature.paletteType.textField
          ? ConfigValues.messages.textBoxAreMandatoryMessage
          : field === Signature.paletteType.signValue.toLowerCase()
            ? ConfigValues.messages.signatureAreMandatoryMessage
            : ConfigValues.messages.checkboxMandatoryMessage,
      header: 'TITLES.SIGNATURE_REQUEST',
      buttons,
      backDrop: false
    };

    this.common.showAlert(alertData).then((confirmation) => {
      callBack(confirmation);
    });
  }
  consoloAppExit(delayBeforeClose = 5000, loaderMessage = ''): void {
    const callBackURL = this.automaticLinkedItems.callbackUrl;
    this.automaticLinkedItems = {};
    this.sessionService.resetLocalStorage();
    this.routeHistory = [];
    this.currentPage = '';
    this.ngxPermissionsService.flushPermissions();
    this.userData = undefined;
    this.messageCount = undefined;
    this.messageList = undefined;
    if (callBackURL != '') {
      window.location.replace(callBackURL);
    } else {
      if (window.close) {
        if (loaderMessage) {
          this.loaderMessage = this.common.getTranslateData(loaderMessage);
        }
        setTimeout(function () {
          this.loaderMessage = '';
          window.close();
        }, delayBeforeClose);
      } else {
        this.trackActivity({
          type: Activity.consoloType,
          name: Activity.consolo,
          des: { desConstant: Activity.consoloDesc },
          linkageId: this.docDetails.actualFileName
        });
      }
    }
  }
  consoloCheckFormType(reqBody?: any): Observable<any> {
    const url = APIs.getFormTypeById;
    const payload = {
      form_id: reqBody
    };
    return this.httpService.doPost({
      endpoint: url,
      payload,
      contentType: 'form',
      parseToString: true,
      loader: false
    });
  }
  reminderForDocument(reqBody?: any): Observable<any> {
    const url = APIs.initiateDocumentReminder;
    const payload = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload,
      contentType: 'form',
      parseToString: false
    });
  }
  validateEmail(email: string, pattern: RegExp = Constants.validationPattern.emailPattern): boolean {
    return pattern.test(email);
  }

  checkAttachmentCount(count: number, maxLength: number): boolean {
    let isLimitExceeded = false;
    if (count > maxLength) {
      const fileLengthErrorMessage = this.common.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
        maxFileLength: maxLength
      });
      this.common.showMessage(fileLengthErrorMessage);
      isLimitExceeded = true;
    }
    return isLimitExceeded;
  }
  reminderForForm(reqBody?: any): Observable<any> {
    const url = APIs.initiateFormReminder;
    const payload = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload,
      contentType: 'form',
      parseToString: false
    });
  }

  notPartOfChatRedirect(): void {
    this.common.dismissPopover();
    const sessionMessage = this.common.getTranslateData('MESSAGES.YOU_ARE_NOT_PART_TO_CHAT_SESSION');
    let buttons = [{ text: this.common.getTranslateData('BUTTONS.GO_TO_INBOX'), confirm: true }];
    this.common
      .showAlert({
        message: sessionMessage,
        cssClass: 'send-form-via-modal  show-button-center',
        backDrop: false,
        buttons: this.disableSideMenu ? [] : buttons,
        noButtons: this.disableSideMenu // property added to show alert popup without buttons for virtual users in appless message
      })
      .then((confirmation) => {
        if (confirmation) {
          this.navCtrl.navigateBack('message-center/messages/active');
          this.common.closeAllAlert();
        }
      });
  }

  setCountryCode(countryCode) {
    return countryCode.charAt(0) === '+' ? countryCode : `+${countryCode}`;
  }

  setCalendarPickerMaxYear(): string {
    return String(moment(new Date(), Constants.dateFormat.YYYYMMDD).add(100, 'years').year());
  }
  /**
   * This method is used to get appName for the given site
   * @param siteId should be a number and used to filter the site details
   * @returns a string value or an empty string
   */
  getSiteAppName(siteId?: number): string {
    // mySites property will have all the site detials of the user
    const mySites = this.userData.mySites;
    let appName = '';
    if (mySites && siteId) {
      mySites.forEach((item, index) => {
        if (item && item.id && item.id === siteId) {
          if (item.enable_support_widget_branding === Constants.configTrue) {
            appName = item.app_name;
          }
        }
      });
    }
    return appName;
  }
  /**
   * To get appName
   * @returns string value
   */
  getAppName(params: { selectedHomeSite?: number; selectedSites?: number[] }): string {
    const appNameOfSelectedHomeSite = params.selectedHomeSite
      ? this.getSiteAppName(params.selectedHomeSite)
      : undefined;
    const appNameOfSelectedSite =
      isPresent(params.selectedSites) && params.selectedSites.length === 1
        ? this.getSiteAppName(Number(params.selectedSites))
        : undefined;
    const appName =
      this.userData.config.app_name && this.isEnableConfig(Config.enableSupportWidgetBranding)
        ? this.userData.config.app_name
        : theme.name;
    return appNameOfSelectedHomeSite
      ? appNameOfSelectedHomeSite
      : appNameOfSelectedSite
        ? appNameOfSelectedSite
        : appName;
  }
  /**
   * updateDocumentDetailedCount function used to set document count from response of mySignatureRequestCount
   * @param data DocumentDetailedCountData
   */
  updateDocumentDetailedCount(data: DocumentDetailedCountData): void {
    const count = data.mySignatureRequestCount;
    if (
      isPresent(count) &&
      isNumberCheck(count.totalPendingCount) &&
      isNumberCheck(count.totalSignedCount) &&
      isNumberCheck(count.totalArchiveCount)
    ) {
      this.documentCount = count;
    }
  }

  getFontSize(height, imageWidth?: number) {
    const heightForFontSize = parseInt(height) - Constants.fontSize.minimumFont;
    let fontSize;
    const responsiveFontSize = imageWidth * Constants.fontSize.fontSizeRation;
    if (responsiveFontSize < Constants.fontSize.maxFont && window.innerWidth < 1400) {
      fontSize = heightForFontSize <= Constants.fontSize.mobileMaxFontSize ? Constants.fontSize.mobileMaxFontSize : responsiveFontSize;
    } else {
      fontSize = Constants.fontSize.maxFont;
    }

    const orginalFontSize =
      heightForFontSize > fontSize
        ? fontSize
        : heightForFontSize < Constants.fontSize.minimumFont
          ? Constants.fontSize.minimumFont
          : heightForFontSize;
    return orginalFontSize;
  }
  /**
   * Function that creates guid for forms. desired format- [time random value - socket client id - crypto random value - form id].
   * @param    {Integer}formId - used in form id part of the guid.
   * @return   {String} guid.
   */
  guidCreate(formId) {
    /**
     * Function that creates crypto random value part of the guid using the Web Crypto API interface.
     * @param    {String}hexa_   any string which is used to convert into crypto random string.
     * @return   {String}        crypto random string.
     */
    function cryptoGuid(hexa_) {
      try {
        const cryptoApi =
          ('undefined' != typeof crypto && crypto.getRandomValues && crypto.getRandomValues.bind(crypto)) ||
          ('undefined' != typeof msCrypto &&
            'function' == typeof msCrypto.getRandomValues &&
            msCrypto.getRandomValues.bind(msCrypto));
        if (!cryptoApi) throw null;
        return hexa_.replace(/[018]/g, (c) => (c ^ (cryptoApi(new Uint8Array(1))[0] & (15 >> (c / 4)))).toString(16)); //convert and return input string into crypto random value string.
      } catch (e) {
        return e;
      }
    }
    /**
     * Function that creates time random string part of guid.
     * @param    {Integer}hexa_   any hexdecimal value to multiply with current time and milliseconds to create random string.
     * @return   {String}         unique string created with current time.
     */
    function dateGuid(hexa_) {
      try {
        const dateObj = new Date();
        if (Object.prototype.toString.call(dateObj) !== '[object Date]') throw null;
        return dateObj.getTime() * dateObj.getUTCMilliseconds()
          ? ((1 + dateObj.getTime() * dateObj.getUTCMilliseconds()) * hexa_).toString(15).substring(2)
          : dateObj.valueOf();
      } catch (e) {
        return e;
      }
    }
    const socketId = this.socketClientId;
    const dateVal = dateGuid(0x10000) ? dateGuid(0x10000) : cryptoGuid('11000'); //time random value part of guid, if returns null then use crypto instead.
    const cryptoRandom = cryptoGuid('1000000') ? cryptoGuid('1000000') : dateGuid(0x100); //crypto part of guid, if returns null then use dateGuid instead.
    let clientId =
      'undefined' != typeof socketId && socketId && socketId != null && socketId != 'null'
        ? socketId.replace(/_/g, '')
        : null;
    if (!clientId) {
      clientId = cryptoGuid('11011100') ? cryptoGuid('11011100') : dateGuid(0x101); //socket client id ,if returns null use crypto or dateGuid instead.
    }
    return dateVal + '-' + clientId + '-' + cryptoRandom + '-' + formId; // return guid.
  }
  /**
   * updateFirstLogin function to update first login for invite user by referral.
   * @param userData
   */
  updateFirstLogin(userData: LoginResponse): void {
    if (
      userData.status === 0 &&
      (userData.registration_type == Constants.duplicateUserRegType.enroll ||
        userData.registration_type == Constants.duplicateUserRegType.enrollType) &&
      userData?.first_login
    ) {
      const reqData = {
        tenantId: userData?.config?.token,
        appUsage: {
          tenantId: userData?.config?.token,
          userInfo: {
            userId: userData?.userId,
            email: userData?.alternate_username,
            referalToken: userData.referral_code
          },
          events: {
            ...Constants.appUsageEvent,
            eventDateTime: moment().format(Constants.dateFormat.ddmmyyyyhma)
          }
        }
      };
      this.httpService
        .doPostByUrl({ payload: reqData, apiUrl: `${environment.apiBase}/${APIs.appUsageWrapper}` }, true)
        .subscribe(
          (response) => {
            if (!response.status) {
              this.trackActivity({
                type: Activity.manageUserEnrollment,
                name: Activity.failureFirstLoginUpdateReferral,
                des: {
                  data: { username: userData.alternate_username, fullResponse: JSON.stringify(response) },
                  desConstant: Activity.failureFirstLoginUpdateReferralDes
                }
              });
            }
          },
          (error) => {
            this.errorHandler(error);
          }
        );
    } else {
      if (userData.first_login) {
        this.trackActivity({
          type: Activity.manageUserEnrollment,
          name: Activity.failureFirstLoginUpdateReferral,
          des: {
            data: {
              username: userData?.alternate_username,
              status: userData?.status,
              registrationType: userData?.registration_type
            },
            desConstant: Activity.failureFirstLoginBeforeWPCallDes
          }
        });
      }
    }
  }
  /**
   * updateChatRoomId When going from one chatroom to another, leave chatroom before updating roomID in shared service.
   * @param sharedRoomId
   * @param newRoomId
   */
  updateChatRoomId(sharedRoomId: number, newRoomId: number, isPush = false): void {
    if (sharedRoomId !== newRoomId) {
      this.socketService.emitEvent(Socket.leaveChatRoom, sharedRoomId);
      this.socketService.subscribeEvent(Socket.userLeave);
    }
    this.roomID = newRoomId;
  }

  /**
   * https://capacitorjs.com/docs/apis/geolocation
   * Use geolocation capacitor plugin to get current lat-long with asking permission
   * Using this plugin resolved permission related issue CHP-7932
   * Reference Link https://stackoverflow.com/a/70348933/10805479
   */
  async getLatLong() {
    const coordinates = await Geolocation.getCurrentPosition();
    if (coordinates.coords && coordinates.coords.latitude && coordinates.coords.longitude) {
      this.vcLatitude = coordinates.coords.latitude;
      this.vcLongitude = coordinates.coords.longitude;
    }
  }

  /**
   * inAppBrowserExecuteAddOnScript is to execute custom scripts from different sources.
   * @param details
   */
  inAppBrowserExecuteAddOnScript(details: { from: string; message: string }): void {
    if (this.browser && this.inAppBrowserData) {
      const message = this.inAppBrowserData.addonScript(details);
      if (details.from == Constants.inAppAddOnFromPath.videoCall) {
        this.inAppBrowserVideoButtonActionExecute();
      }
      this.browser.executeScript({ code: message });
    }
  }
  /**
   * inAppBrowserVideoButtonActionExecute Displays popover and
   */
  inAppBrowserVideoButtonActionExecute(): void {
    if (this.browser && this.inAppBrowserData) {
      this.browser.insertCSS({
        file: this.inAppBrowserData.videoCallPopCustomStyle
      });
      this.isVideoButtonCleared = false;
      this.videoButtonInterval = setInterval(() => {
        this.browser.executeScript({ code: this.inAppBrowserData.getVideoCallButton }).then((values) => {
          if (values != '' && !this.isVideoButtonCleared) {
            this.browser.executeScript({ code: this.inAppBrowserData.setVideoCallButton });
            this.videoInappResponse.next(values);
            clearInterval(this.videoButtonInterval);
            this.isVideoButtonCleared = true;
            if (values === Constants.acceptCall) {
              this.browser.hide();
            }
          }
        });
      });
    }
  }

  lockDevice(orientation) {
    const myScreenOrientation = <ExtendedScreenOrientation>window.screen.orientation;
    myScreenOrientation.lock(orientation);
  }

  unlockDevice() {
    window.screen.orientation.unlock();
  }

  inviteAndForwardPushNotification(deepLinkData, userDetails, sendNotificationData, notificationData: object = {}): void {
    const deepLinking: any = {
      state: Constants.deepLinkingStates.groupChat,
      stateParams: {
        targetID: String(deepLinkData.roomId),
        targetName: Constants.pushNotificationGroupChatTarget
      },
      activeMessage: {
        sent: new Date().getTime() / 1000,
        messageType: deepLinkData?.messageType || Constants.configFalse,
        baseId: deepLinkData?.baseId ? deepLinkData?.baseId : Constants.defaultFlagValue,
        ...userDetails,
        message_group_id: deepLinkData?.messageGroupId
          ? deepLinkData?.messageGroupId.toString()
          : Constants.configFalse,
        createdby: deepLinkData?.createdUser,
        chatroomid: String(deepLinkData?.roomId),
        msg_flag: Constants.defaultFlagValue,
        msg_flag_data_id: null,
        prev_msg_flag: Constants.defaultFlagValue,
        selectedTenantId: deepLinkData?.selectedTenantId || '',
        priorityId: deepLinkData?.priorityId || MessagePriority.NORMAL
      },
      tenantId: this.userData.tenantId,
      tenantName: this.userData.tenantName
    };

    this.sentPushNotification(
      sendNotificationData.chatRoomOrToId,
      sendNotificationData.userId,
      sendNotificationData.pushMessage,
      sendNotificationData.privilegeKey,
      deepLinking,
      sendNotificationData.showDoubleVerificationStatus,
      notificationData
    );
  }

  resetSelectedDateFilterData() {
    localStorage.removeItem(Constants.storageKeys.dateRangeFilterForms);
    localStorage.removeItem(Constants.storageKeys.dateRangeFilterFormsDateOptions);
    this.selectedDateOptions = Constants.filterSelectedOptions.lastMonth;
    this.selectedDateRange = JSON.parse(JSON.stringify(Constants.resetSelectedDateRange));
  }

  async shareDocument(url): Promise<void> {
    await Share.share({
      url
    });
  }
  handleApiError(errorInfo: any): string {
    let errorMessage;
    if (errorInfo.error.customerMessages) {
      errorMessage = errorInfo.error.customerMessages[0];
    } else if (errorInfo.error.error) {
      errorMessage = errorInfo.error.error.error;
    }
    if (errorMessage) {
      return errorMessage;
    }
    else {
      return this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
    }
  }
  handleErrorWithToast(
    error: any,
    toastCallback: (message: string) => void
  ): void {
    const errorMessage = this.handleApiError(error);
    toastCallback(errorMessage);
  }
  redirectPage(page: string) {
    this.router.navigateByUrl(page);
  }

  /**
   * Dynamically loads the Google Maps API script into the document.
   * Ensures the script is added only once to avoid duplicate loading.
   */
  loadGoogleMap(): void {
    const existingScript = document.querySelector('script[src*="maps.google.com/maps/api/js"]');
    if (!existingScript) {
      const script = document.createElement('script');
      script.src = `https://maps.google.com/maps/api/js?key=${environment.mapAPIKey}`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }
  }

  /** open modal for select admission */
  async selectAdmission(params: any, chooseRecipientsPage: typeof ChooseRecipientsPage, admissionComponent: typeof AdmissionComponent, callBack?: any): Promise<void> {
    const modal = await this.modalController.create({
      component: admissionComponent,
      cssClass: 'common-advanced-select',
      componentProps: {
        from: params.from || '',
        patientID: params.userId,
        siteIds: params?.siteIds || '',
        initialAdmissions: params?.admissions || [],
        admissionID: params?.admissionId || '',
      }
    });
    await modal.present();
    await modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        const admissionId = modalData.data?.admissionId;
        if (callBack) {
          callBack(modalData.data);
        } else {
          let selectedAssociatePatient;
          if (params.userType === PatientType.SINGLE_ASSOCIATED_PATIENT) {
            selectedAssociatePatient = params.userId;
          }
          this.newChatAction(chooseRecipientsPage, selectedAssociatePatient, admissionId);
        }
      } else if (callBack) {
        callBack(false);
      }
    });
  }
  /** Based on the admission config the label will change with admission
   * @param key Key to get the label and the same will be suffixed with LABELS.ADMISSION_ if multi admission is enabled
   */
  getSiteLabelBasedOnMultiAdmission(key: string) {
    const baseLabel = this.isEnableConfig(Config.enableMultiAdmissions) ? 'LABELS.ADMISSION_' : 'LABELS.';
    return baseLabel + key;
  }

  generateLabel(user): string {
    const mrn = this.common.getTranslateData('GENERAL.MRN');
    const enrolled = this.common.getTranslateData('GENERAL.ENROLLED');
    const virtual = this.common.getTranslateData('GENERAL.VIRTUAL');
    let displayLabel = '';
    let labelObj = {
      displayName : user.displayname,
      dob: '',
      identityValue:'',
      userType:'',
      siteName:'' 
    }
    
    if (user?.dob) {
      labelObj.dob = formatDate(user?.dob);
    }

    if (user?.IdentityValue) {
      labelObj.identityValue = `${mrn}: [${user?.IdentityValue}]`;
    }
    labelObj.userType = user?.passwordStatus === 'true' || user?.passwordStatus === '1' ? `(${enrolled})` : `(${virtual})`;
    if (user?.sitename) {
      labelObj.siteName = `- ${user?.sitename}`;
    }
    
    displayLabel = this.setActivityDescription(labelObj, Constants.userLabel);
    return displayLabel;
  }

  /**
   * Restrict message unread count and listing update based on the chat thread filter applied
   * @param eventData 
   * @returns true - if no chat thread filter applied or chat thread filter applied and event data chat thread type is in filter or if the location is home page
   */
  updateInboxOnThreadTypeFilter(eventData): boolean {
    const storedValue  = deepParseJSON(getValueFromSession(Constants.storageKeys.persistentData))
    const chatThreadTypes  = storedValue[Constants.storageKeys.activeMessageFilterKeys] 
    ? storedValue[Constants.storageKeys.activeMessageFilterKeys].chatThreadTypes : [];
    const { chatThreadType } = eventData;
  
    const isActivePath = location.pathname === '/message-center/messages/active';
    const hasValidChatThreadType = chatThreadTypes.length && chatThreadType && chatThreadTypes.includes(messageTypeAndCategory[chatThreadType]);
  
    return !chatThreadType || !chatThreadTypes.length || (isActivePath && hasValidChatThreadType) || (chatThreadTypes.length && !isActivePath);
  }
}
