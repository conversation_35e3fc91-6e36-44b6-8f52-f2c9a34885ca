name: iOS Build Test

# This workflow is for testing the iOS build setup
# Use this to validate your configuration before using the full CI/CD pipeline

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'qa6'
        type: choice
        options:
        - qa6
        - qa7
        - qa8
        - stage
        - production
      test_type:
        description: 'Test type'
        required: true
        default: 'web-only'
        type: choice
        options:
        - web-only
        - ios-simulator
        - full-test

env:
  NODE_VERSION: '18.x'
  XCODE_VERSION: '15.2'

jobs:
  test-web-build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install Ionic CLI
      run: npm install -g @ionic/cli
      
    - name: Test web build
      run: |
        echo "Testing web build for environment: ${{ github.event.inputs.environment }}"
        ionic build --configuration=${{ github.event.inputs.environment }}
        
    - name: Verify build output
      run: |
        echo "Checking build output..."
        ls -la www/
        echo "Build completed successfully!"
        
    - name: Upload web build for iOS testing
      if: github.event.inputs.test_type != 'web-only'
      uses: actions/upload-artifact@v4
      with:
        name: web-build-test
        path: www/
        retention-days: 1

  test-ios-setup:
    needs: test-web-build
    runs-on: macos-14
    if: github.event.inputs.test_type != 'web-only'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Download web build
      uses: actions/download-artifact@v4
      with:
        name: web-build-test
        path: www/
        
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install CLI tools
      run: npm install -g @ionic/cli @capacitor/cli
      
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: ${{ env.XCODE_VERSION }}
        
    - name: Setup Ruby for CocoaPods
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.1'
        bundler-cache: true
        
    - name: Install CocoaPods
      run: |
        gem install cocoapods
        pod --version
        
    - name: Test Capacitor sync
      run: |
        echo "Testing Capacitor sync..."
        npx cap sync ios
        echo "Capacitor sync completed!"
        
    - name: Test CocoaPods installation
      run: |
        echo "Testing CocoaPods installation..."
        cd ios/App
        pod install --repo-update
        echo "CocoaPods installation completed!"
        
    - name: Test iOS build (Simulator only)
      if: github.event.inputs.test_type == 'ios-simulator' || github.event.inputs.test_type == 'full-test'
      run: |
        echo "Testing iOS build for simulator..."
        cd ios/App
        xcodebuild -workspace App.xcworkspace \
          -scheme App \
          -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest' \
          -configuration Debug \
          build
        echo "iOS simulator build completed!"
        
    - name: List available simulators
      run: |
        echo "Available iOS simulators:"
        xcrun simctl list devices available
        
    - name: Verify iOS project structure
      run: |
        echo "iOS project structure:"
        ls -la ios/App/
        echo ""
        echo "Capacitor configuration:"
        cat capacitor.config.ts
        
  test-summary:
    needs: [test-web-build, test-ios-setup]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Test Summary
      run: |
        echo "## iOS Build Test Summary"
        echo ""
        echo "**Environment:** ${{ github.event.inputs.environment }}"
        echo "**Test Type:** ${{ github.event.inputs.test_type }}"
        echo ""
        echo "**Results:**"
        echo "- Web Build: ${{ needs.test-web-build.result }}"
        if [[ "${{ github.event.inputs.test_type }}" != "web-only" ]]; then
          echo "- iOS Setup: ${{ needs.test-ios-setup.result }}"
        fi
        echo ""
        if [[ "${{ needs.test-web-build.result }}" == "success" ]] && [[ "${{ needs.test-ios-setup.result }}" == "success" || "${{ github.event.inputs.test_type }}" == "web-only" ]]; then
          echo "✅ **All tests passed!** Your iOS CI/CD setup is ready."
          echo ""
          echo "**Next Steps:**"
          echo "1. Set up code signing certificates (for archive builds)"
          echo "2. Configure GitHub secrets for App Store deployment"
          echo "3. Test the full iOS CI/CD pipeline"
        else
          echo "❌ **Some tests failed.** Please check the logs above."
          echo ""
          echo "**Common Issues:**"
          echo "- Missing NPM_AUTH_TOKEN secret"
          echo "- Node.js dependency conflicts"
          echo "- Capacitor configuration issues"
          echo "- CocoaPods installation problems"
        fi
