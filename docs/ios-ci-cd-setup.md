# iOS CI/CD Pipeline Setup Guide

This guide explains how to set up and use the GitHub Actions CI/CD pipeline for iOS app deployment.

## Overview

The iOS CI/CD pipeline consists of 4 main jobs:

1. **Web Build & Test** - Builds the Angular/Ionic web app and runs tests
2. **iOS Build** - Builds the iOS app using Capacitor and Xcode
3. **Deploy to App Store** - Uploads the app to App Store Connect (optional)
4. **Notify** - Sends notifications about pipeline status

## Prerequisites

### 1. Apple Developer Account Setup

- Active Apple Developer Program membership
- App registered in App Store Connect
- Bundle ID: `com.citushealth.cb` (as configured in capacitor.config.ts)

### 2. Code Signing Certificates

You need to create and export the following certificates:

#### Distribution Certificate (for App Store builds)
```bash
# Generate certificate signing request (CSR)
# Upload CSR to Apple Developer Portal
# Download the certificate and install in Keychain
# Export as .p12 file with password
```

#### Development Certificate (for development builds)
```bash
# Similar process for development certificate
```

### 3. Provisioning Profiles

Create provisioning profiles for:
- **Development** - for testing on devices
- **Ad Hoc** - for internal distribution
- **App Store** - for App Store submission

## GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

### Required Secrets

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `NPM_AUTH_TOKEN` | GitHub Packages token | `ghp_xxxxxxxxxxxx` |
| `IOS_CERTIFICATES_P12` | Base64 encoded .p12 certificate | `MIIKxAIBAzCCCn4GCSqGSIb3...` |
| `IOS_CERTIFICATES_PASSWORD` | Password for .p12 certificate | `your-cert-password` |
| `IOS_TEAM_ID` | Apple Developer Team ID | `ABCD123456` |
| `APPSTORE_ISSUER_ID` | App Store Connect API Issuer ID | `12345678-1234-1234-1234-123456789012` |
| `APPSTORE_KEY_ID` | App Store Connect API Key ID | `ABCD123456` |
| `APPSTORE_PRIVATE_KEY` | App Store Connect API Private Key | `-----BEGIN PRIVATE KEY-----...` |
| `APPSTORE_USERNAME` | Apple ID for App Store uploads | `<EMAIL>` |
| `APPSTORE_PASSWORD` | App-specific password | `abcd-efgh-ijkl-mnop` |

### How to Get These Values

#### 1. NPM_AUTH_TOKEN
```bash
# Already configured in your existing workflows
```

#### 2. iOS Certificates (.p12)
```bash
# Export certificate from Keychain Access
# Convert to base64
base64 -i YourCertificate.p12 | pbcopy
```

#### 3. App Store Connect API Keys
1. Go to App Store Connect → Users and Access → Keys
2. Create new API key with Developer role
3. Download the .p8 file
4. Note the Key ID and Issuer ID

#### 4. App-Specific Password
1. Go to appleid.apple.com
2. Sign in with your Apple ID
3. Generate app-specific password for GitHub Actions

## Pipeline Usage

### Automatic Triggers

The pipeline runs automatically on:
- Push to `main`, `develop`, or `release/*` branches
- Pull requests to `main` or `develop` branches

### Manual Triggers

You can manually trigger the pipeline with custom parameters:

1. Go to GitHub Actions tab
2. Select "iOS CI/CD Pipeline"
3. Click "Run workflow"
4. Choose parameters:
   - **Environment**: qa6, qa7, qa8, stage, production
   - **Build Type**: development, adhoc, appstore

### Build Types Explained

#### Development Build
- Builds for iOS Simulator
- No code signing required
- Fast build for testing
- Suitable for PR checks

#### Ad Hoc Build
- Builds for physical devices
- Requires development/ad hoc provisioning profile
- For internal testing and QA
- Creates IPA file

#### App Store Build
- Builds for App Store submission
- Requires distribution certificate and App Store provisioning profile
- Automatically uploads to App Store Connect
- Creates IPA file

## Environment Configurations

The pipeline supports multiple environments based on your Angular configurations:

- **qa6** - QA Environment 6 (uses `environment.qa6.ts`)
- **qa7** - QA Environment 7 (uses `environment.qa7.ts`)
- **qa8** - QA Environment 8 (uses `environment.qa8.ts`)
- **stage** - Staging Environment (uses `environment.stage.ts`)
- **production** - Production Environment (uses `environment.prod.ts`)

## Workflow Steps Breakdown

### 1. Web Build & Test Job
```yaml
- Checkout code
- Setup Node.js with caching
- Install dependencies
- Run linting
- Run unit tests with coverage
- Build Angular/Ionic app for specified environment
- Upload build artifacts
```

### 2. iOS Build Job
```yaml
- Setup macOS runner with Xcode
- Download web build artifacts
- Install iOS dependencies (CocoaPods)
- Sync Capacitor
- Import code signing certificates (if needed)
- Download provisioning profiles (if needed)
- Build iOS app (simulator or archive)
- Export IPA (if archive build)
- Upload IPA artifacts
```

### 3. Deploy Job (App Store only)
```yaml
- Download IPA artifact
- Upload to App Store Connect using altool
```

## Troubleshooting

### Common Issues

#### 1. Code Signing Errors
```bash
# Check certificate validity
security find-identity -v -p codesigning

# Verify provisioning profile
security cms -D -i YourProfile.mobileprovision
```

#### 2. CocoaPods Issues
```bash
# Clear CocoaPods cache
pod cache clean --all
pod repo update
```

#### 3. Xcode Build Errors
```bash
# Clean build folder
xcodebuild clean -workspace App.xcworkspace -scheme App

# Check available simulators
xcrun simctl list devices
```

#### 4. Capacitor Sync Issues
```bash
# Force sync
npx cap sync ios --force

# Check capacitor configuration
npx cap doctor
```

### Debug Tips

1. **Enable verbose logging** in workflow by adding:
   ```yaml
   - name: Debug Step
     run: set -x; your-command-here
   ```

2. **Check Xcode logs** in the workflow output

3. **Verify environment files** exist for the selected configuration

4. **Test locally** before pushing:
   ```bash
   # Test web build
   ionic build --configuration=qa6
   
   # Test iOS build
   npx cap sync ios
   cd ios/App && xcodebuild -workspace App.xcworkspace -scheme App -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest' build
   ```

## Security Best Practices

1. **Never commit certificates or private keys** to the repository
2. **Use GitHub Secrets** for all sensitive information
3. **Rotate certificates and API keys** regularly
4. **Limit API key permissions** to minimum required
5. **Use app-specific passwords** instead of main Apple ID password

## Monitoring and Notifications

The pipeline includes basic success/failure notifications. You can extend this by:

1. **Adding Slack notifications**:
   ```yaml
   - name: Notify Slack
     uses: 8398a7/action-slack@v3
     with:
       status: ${{ job.status }}
       webhook_url: ${{ secrets.SLACK_WEBHOOK }}
   ```

2. **Adding email notifications** through GitHub's built-in features

3. **Integrating with monitoring tools** like Datadog or New Relic

## Next Steps

1. Set up all required secrets in GitHub
2. Test the pipeline with a development build
3. Gradually test ad hoc and App Store builds
4. Set up monitoring and notifications
5. Document any project-specific customizations

For questions or issues, please refer to the project documentation or contact the development team.
