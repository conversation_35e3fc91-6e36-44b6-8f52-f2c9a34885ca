import { VideoCallComponent } from 'src/app/components/video-call/video-call.component';
import { APP_INITIALIZER, NgModule, CUSTOM_ELEMENTS_SCHEMA, Injector } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { Router, RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
// import { StatusBar } from '@ionic-native/status-bar/ngx';
import { AppComponent } from 'src/app/app.component';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { HttpClientModule, HTTP_INTERCEPTORS, HttpClient } from '@angular/common/http';
import { HttpInterceptorService } from 'src/app/services/http-interceptor/http-interceptor.service';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { GraphQLModule } from 'src/app/graphql.module';
import { LoaderComponent } from 'src/app/components/loader/loader.component';
import { CommonMessageComponentModule } from 'src/app/components/common-message/common-message.module';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from 'src/environments/environment';
import { MenuComponentModule } from 'src/app/components/menu/menu.component.module';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import { NgxPermissionsModule } from 'ngx-permissions';
import { Diagnostic } from '@awesome-cordova-plugins/diagnostic/ngx';
import { DocumentPicker } from '@awesome-cordova-plugins/document-picker/ngx';
import { Clipboard } from '@awesome-cordova-plugins/clipboard/ngx';
import { KeychainTouchId } from '@ionic-native/keychain-touch-id/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { SessionService } from 'src/app/services/session-service/session.service';
import { isPresent } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { getValueFromSession, setKeyToSession } from 'src/app/utils/storage-utils';
import { PageRoutes } from 'src/app/constants/page-routes';
import { pageTransition } from 'src/app/ page-transition';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { OKTA_CONFIG, OktaAuthModule } from '@okta/okta-angular';
import { OktaAuth } from '@okta/okta-auth-js';
import { OktaService } from './services/okta/okta.service';
import { Activity } from 'src/app/constants/activity';
import { SharedService } from 'src/app/services/shared-service/shared.service';

/**
 * Initialize Okta token manager when app restarts with existing session
 * This ensures Okta tokens can be refreshed even when login page is bypassed
 * @param injector Injector to access OKTA_CONFIG
 */
async function initializeOktaOnAppRestart(injector: Injector): Promise<void> {
  try {
    const oktaLogin = localStorage.getItem(Constants.storageKeys.oktaLogin);
    const oktaTokenStorage = localStorage.getItem(Constants.storageKeys.oktaTokenStorage);

    if (!environment.oktaFlow || !oktaLogin || !oktaTokenStorage) {
      return;
    }

    const oktaConfig = injector.get(OKTA_CONFIG, null);
    if (!oktaConfig || !oktaConfig.oktaAuth) {
      return;
    }

    const { oktaAuth } = oktaConfig;
    const sharedService = injector.get(SharedService);

    try {
      const currentTokens = oktaAuth.tokenManager.getTokensSync();

      if (!currentTokens.accessToken && oktaTokenStorage) {
        try {
          const storedTokens = JSON.parse(oktaTokenStorage);
          await sharedService.setTokensFollowingOktaRequirements(oktaAuth, storedTokens);
        } catch {
          // Error parsing stored tokens
        }
      } else if (currentTokens.accessToken) {
        try {
          await sharedService.validateAndRenewOktaTokens();
        } catch {
          // Token renewal failed
        }
      }
    } catch {
      // Error checking current tokens
    }

    oktaAuth.tokenManager.on('renewed', (key: string, newToken: any) => {
      if (key === 'accessToken') {
        sharedService.updateNewTokenDetails(newToken);
        localStorage.setItem(Constants.storageKeys.oktaTokenExpiry, newToken.expiresAt.toString());
        const allTokens = oktaAuth.tokenManager.getTokensSync();
        localStorage.setItem(Constants.storageKeys.oktaTokenStorage, JSON.stringify(allTokens));
      }
    });

    oktaAuth.tokenManager.on('error', async (err: any) => {
      const isTokenRenewalError =
        err.name === 'OAuthError' && (err.message?.includes('The refresh token is invalid or expired') || err.errorCode === 'login_required');

      if (isTokenRenewalError) {
        if (err.message?.includes('The refresh token is invalid or expired') || err.error === 'invalid_grant') {
          try {
            const tokens = await oktaAuth.token.renewTokens();
            oktaAuth.tokenManager.setTokens(tokens);
            return;
          } catch {
            // Renewal failed - proceed to cleanup
          }
        }
        const tokenExpiredMessage = this.common.getTranslateData('VALIDATION_MESSAGES.SESSION_EXPIRED');
        this.common.showMessage(tokenExpiredMessage);
        sharedService.logout();
      }
    });

    try {
      await oktaAuth.start();
    } catch {
      // Service may already be running
    }
  } catch {
    // Error initializing Okta on app restart
  }
}

export function createTranslateLoader(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [AppComponent, LoaderComponent, VideoCallComponent],
  imports: [
    BrowserModule,
    IonicModule.forRoot({
      scrollPadding: false,
      innerHTMLTemplatesEnabled: true,
      scrollAssist: true,
      navAnimation: pageTransition
    }),
    AppRoutingModule,
    HttpClientModule,
    OktaAuthModule,
    CommonMessageComponentModule,
    MenuComponentModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient]
      }
    }),
    GraphQLModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: environment.enableServiceWorker,
      registrationStrategy: 'registerImmediately'
    }),
    NgIdleKeepaliveModule.forRoot(),
    NgxPermissionsModule.forRoot()
  ],
  providers: [
    OktaService, // Add OktaService to providers
    {
      provide: OKTA_CONFIG,
      deps: [OktaService], // Specify OktaService as a dependency
      useFactory: (oktaService: OktaService) => {
        const oktaConfig = oktaService.getOktaConfig(); // Retrieve oktaConfig from OktaService
        const oktaAuth = new OktaAuth(oktaConfig);
        return {
          oktaAuth,
          onAuthRequired: (oktaAuth: OktaAuth, injector: Injector) => {
            const triggerLogin = () => {
              // Redirect the user to your custom login page
              const router = injector.get(Router);
              router.navigate(['/login']);
            };
            if (!oktaAuth.authStateManager.getPreviousAuthState()?.isAuthenticated) {
              // App initialization stage
              triggerLogin();
            } else {
              // Ask the user to trigger the login process during token autoRenew process
            }
          }
        };
      }
    },
    // StatusBar,
    InAppBrowser,
    Diagnostic,
    DocumentPicker,
    Clipboard,
    KeychainTouchId,
    {
      provide: APP_INITIALIZER,
      useFactory: resourceProviderFactory,
      deps: [SessionService, Router, SharedService, Injector],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorService,
      multi: true
    },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    SQLite,
    NativeStorage
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  bootstrap: [AppComponent]
})
export class AppModule {}

/**
 * @description Initialization function to identify route and fetch data using token from local storage before app start.
 * @param sessionService SessionService
 * @param router Router
 * @param sharedService SharedService
 * @returns any
 */
export function resourceProviderFactory(sessionService: SessionService, router: Router, sharedService: SharedService, injector: Injector): any {
  sessionService.sessionLoader = true;
  sessionService.setBrandedConfig();
  const authToken = getValueFromSession(Constants.storageKeys.authToken);
  let currentUrl = '/';
  const routeHistory = JSON.parse(sessionStorage.getItem(Constants.storageKeys.routeHistory));
  if (routeHistory && routeHistory.length > 0 && routeHistory[routeHistory.length - 1]) {
    currentUrl = routeHistory[routeHistory.length - 1].url;
  }
  sessionService.applessMessagingFlow = false;
  const currentPathMessage = window.location.href.split(PageRoutes.applessURLPathMessage);
  const currentPathForm = window.location.href.split(PageRoutes.applessURLPathForm);
  const currentPathDoc = window.location.href.split(PageRoutes.applessURLPathDoc);
  const currentPathVideo = window.location.href.split(PageRoutes.applessURLPathVideo);
  const currentPathDownload = window.location.href.split(PageRoutes.applessURLPathDownload);
  const currentPathVisit = window.location.href.split(PageRoutes.applessURLPathVisitView);
  const currentPathUserConsent = window.location.href.split(PageRoutes.appLessUserConsent);
  let applessPath = '';
  if (window.location.href.includes(PageRoutes.applessURLPathMessage)) {
    applessPath = `${PageRoutes.applessURLPathMessage}${currentPathMessage[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathDoc)) {
    applessPath = `${PageRoutes.applessURLPathDoc}${currentPathDoc[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathForm)) {
    applessPath = `${PageRoutes.applessURLPathForm}${currentPathForm[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathVideo)) {
    applessPath = `${PageRoutes.applessURLPathVideo}${currentPathVideo[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathDownload)) {
    applessPath = `${PageRoutes.applessURLPathDownload}${currentPathDownload[1]}`;
  } else if (window.location.href.includes(PageRoutes.applessURLPathVisitView)) {
    applessPath = `${PageRoutes.applessURLPathVisitView}${currentPathVisit[1]}`;
  } else if (window.location.href.includes(PageRoutes.appLessUserConsent)) {
    applessPath = `${PageRoutes.appLessUserConsent}${currentPathUserConsent[1]}`;
  } else if (window.location.href.includes(PageRoutes.appLess)) {
    applessPath = `${PageRoutes.appLess}${window.location.href.split(PageRoutes.appLess)[1]}`;
  }
  const authRoutes = [PageRoutes.root, PageRoutes.login, PageRoutes.forgotPassword];
  if (isPresent(applessPath)) {
    return () => {
      // Track activity for app-less link redirection
      sharedService.trackActivity({
        type: Activity.appless,
        name: Activity.pageNavigation,
        des: {
          data: {
            applessPath,
            applessLink: window.location.href
          },
          desConstant: Activity.applessRedirectPathDes
        }
      });
      router.navigateByUrl(applessPath);
    };
  } else if (authToken) {
    if (!navigator.onLine) {
      // Track activity for offline mode redirection
      sharedService.trackActivity({
        name: Activity.pageNavigation,
        des: { desConstant: Activity.redirectToLoginOfflineModeDes }
      });
      return () => {
        sessionService.sessionLoader = false;
        router.navigate(['/home']);
      };
    }

    return () => {
      return sessionService.getSessionData().then(
        (data) => {
          sessionService.sessionLoader = false;
          if (!data || !data.response) {
            if (data.status === 505 || data.status === 401 || authRoutes.includes(currentUrl) === false) {
              // Track activity for no session data redirection
              sharedService.trackActivity({
                name: Activity.pageNavigation,
                des: { desConstant: Activity.redirectToLoginNoSessionDateDes }
              });
              sessionService.resetLocalStorage();
              router.navigate([PageRoutes.login]);
            }
          } else {
            sessionService.userData = data.response;
            sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.falseAsString);
            setKeyToSession({ key: Constants.storageKeys.authToken, value: data.response.authenticationToken });
            const oktaTokenStorage = localStorage.getItem(Constants.storageKeys.oktaTokenStorage);
            if (environment.oktaFlow && isPresent(oktaTokenStorage)) {
              initializeOktaOnAppRestart(injector).catch(() => {
                // Okta initialization failed, continue without it
              });
            }
            if (data.response.mySites.length > 1 && data.response.group !== '3' && data.response.config.enable_multisite === '1') {
              let selectedSiteFilter = data.response.mySites;
              if (data.response.defaultSitesFilter) {
                selectedSiteFilter = data.response.defaultSitesFilter;
              }
              sessionService.setDefaultSelectedIds(selectedSiteFilter);
            }
          }
        },
        (err) => {
          sessionService.sessionLoader = false;
          if (authRoutes.includes(currentUrl) === false) {
            sessionService.resetLocalStorage();
            // Track activity for session data error redirection
            sharedService.trackActivity({
              name: Activity.pageNavigation,
              des: { desConstant: Activity.getSessionDataErrorRedirectDes }
            });
            router.navigate([PageRoutes.login]);
          }
        }
      );
    };
  } else {
    return () => {
      sessionService.sessionLoader = false;
      if (authRoutes.includes(currentUrl) === false) {
        sessionService.resetLocalStorage();
        // Track activity for redirection to login
        sharedService.trackActivity({
          name: Activity.pageNavigation,
          des: { desConstant: Activity.redirectToLoginDes }
        });
        router.navigate([PageRoutes.login]);
      }
    };
  }
}
