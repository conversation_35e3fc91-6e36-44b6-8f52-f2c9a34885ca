name: iOS CI/CD Pipeline

on:
  push:
    branches: [ "main", "develop", "release/*" ]
  pull_request:
    branches: [ "main", "develop" ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'qa6'
        type: choice
        options:
        - qa6
        - qa7
        - qa8
        - stage
        - production
      build_type:
        description: 'Build type'
        required: true
        default: 'development'
        type: choice
        options:
        - development
        - adhoc
        - appstore

env:
  NODE_VERSION: '18.x'
  XCODE_VERSION: '15.2'
  IOS_SIMULATOR_DEVICE: 'iPhone 15'

jobs:
  # Job 1: Build and Test Web App
  web-build-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Clean npm cache
      run: npm cache clean --force
      
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install Angular CLI
      run: npm install -g @angular/cli @capacitor/cli
      
    - name: Lint code
      run: npm run lint
      continue-on-error: true
      
    - name: Run unit tests
      run: |
        sudo apt-get update
        wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
        sudo apt install ./google-chrome-stable_current_amd64.deb
        node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng test --no-watch --no-progress --browsers=ChromeHeadless --code-coverage
        
    - name: Build web app
      run: |
        ENVIRONMENT=${{ github.event.inputs.environment || 'qa6' }}
        echo "Building for environment: $ENVIRONMENT"
        ionic build --configuration=$ENVIRONMENT
        
    - name: Upload web build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: web-build-${{ github.event.inputs.environment || 'qa6' }}
        path: www/
        retention-days: 7
        
    - name: Upload test coverage
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/
        retention-days: 7

  # Job 2: iOS Build and Archive
  ios-build:
    needs: web-build-test
    runs-on: macos-14
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Download web build artifacts
      uses: actions/download-artifact@v4
      with:
        name: web-build-${{ github.event.inputs.environment || 'qa6' }}
        path: www/
        
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install Ionic and Capacitor CLI
      run: npm install -g @ionic/cli @capacitor/cli
      
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: ${{ env.XCODE_VERSION }}
        
    - name: Setup Ruby for CocoaPods
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.1'
        bundler-cache: true
        
    - name: Install CocoaPods
      run: |
        gem install cocoapods
        pod --version
        
    - name: Sync Capacitor
      run: |
        npx cap sync ios
        
    - name: Install iOS dependencies
      run: |
        cd ios/App
        pod install --repo-update
        
    - name: Import Code-Signing Certificates
      uses: Apple-Actions/import-codesign-certs@v2
      if: github.event.inputs.build_type != 'development'
      with:
        p12-file-base64: ${{ secrets.IOS_CERTIFICATES_P12 }}
        p12-password: ${{ secrets.IOS_CERTIFICATES_PASSWORD }}
        
    - name: Install Provisioning Profile
      if: github.event.inputs.build_type != 'development'
      run: |
        # Create provisioning profiles directory
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles

        # Install the provisioning profile based on environment and build type
        if [[ "${{ github.event.inputs.environment }}" == "stage" ]] || [[ "${{ github.event.inputs.build_type }}" == "adhoc" ]]; then
          # Use the Stage provisioning profile you provided
          echo "${{ secrets.IOS_STAGE_PROVISIONING_PROFILE }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/CitusHealth_Stage.mobileprovision
          echo "Installed Stage provisioning profile"
        elif [[ "${{ github.event.inputs.build_type }}" == "appstore" ]]; then
          # Use App Store provisioning profile (you'll need to add this)
          echo "${{ secrets.IOS_APPSTORE_PROVISIONING_PROFILE }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/CitusHealth_AppStore.mobileprovision
          echo "Installed App Store provisioning profile"
        fi

        # List installed profiles for verification
        echo "Installed provisioning profiles:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/
        
    - name: Build iOS App (Development)
      if: github.event.inputs.build_type == 'development' || github.event_name != 'workflow_dispatch'
      run: |
        cd ios/App
        xcodebuild -workspace App.xcworkspace \
          -scheme App \
          -destination 'platform=iOS Simulator,name=${{ env.IOS_SIMULATOR_DEVICE }},OS=latest' \
          -configuration Debug \
          build
          
    - name: Build iOS App (Archive)
      if: github.event.inputs.build_type != 'development' && github.event_name == 'workflow_dispatch'
      run: |
        cd ios/App
        CONFIGURATION=${{ github.event.inputs.build_type == 'appstore' && 'Release' || 'Debug' }}
        xcodebuild -workspace App.xcworkspace \
          -scheme App \
          -configuration $CONFIGURATION \
          -destination 'generic/platform=iOS' \
          -archivePath App.xcarchive \
          archive
          
    - name: Export IPA
      if: github.event.inputs.build_type != 'development' && github.event_name == 'workflow_dispatch'
      run: |
        cd ios/App
        # Create export options plist
        cat > ExportOptions.plist << EOF
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
          <key>method</key>
          <string>${{ github.event.inputs.build_type == 'appstore' && 'app-store' || 'ad-hoc' }}</string>
          <key>teamID</key>
          <string>${{ secrets.IOS_TEAM_ID }}</string>
          <key>uploadBitcode</key>
          <false/>
          <key>uploadSymbols</key>
          <true/>
          <key>compileBitcode</key>
          <false/>
        </dict>
        </plist>
        EOF
        
        xcodebuild -exportArchive \
          -archivePath App.xcarchive \
          -exportPath ./build \
          -exportOptionsPlist ExportOptions.plist
          
    - name: Upload IPA Artifact
      if: github.event.inputs.build_type != 'development' && github.event_name == 'workflow_dispatch'
      uses: actions/upload-artifact@v4
      with:
        name: ios-app-${{ github.event.inputs.environment || 'qa6' }}-${{ github.event.inputs.build_type || 'development' }}
        path: ios/App/build/*.ipa
        retention-days: 30

  # Job 3: Deploy to App Store Connect (Optional)
  deploy-appstore:
    needs: ios-build
    runs-on: macos-14
    if: github.event.inputs.build_type == 'appstore' && github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Download IPA
      uses: actions/download-artifact@v4
      with:
        name: ios-app-${{ github.event.inputs.environment || 'qa6' }}-appstore
        path: ./
        
    - name: Upload to App Store Connect
      run: |
        xcrun altool --upload-app \
          --type ios \
          --file *.ipa \
          --username "${{ secrets.APPSTORE_USERNAME }}" \
          --password "${{ secrets.APPSTORE_PASSWORD }}"

  # Job 4: Notify on completion
  notify:
    needs: [web-build-test, ios-build]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify Success
      if: needs.web-build-test.result == 'success' && needs.ios-build.result == 'success'
      run: |
        echo "✅ iOS CI/CD Pipeline completed successfully!"
        echo "Environment: ${{ github.event.inputs.environment || 'qa6' }}"
        echo "Build Type: ${{ github.event.inputs.build_type || 'development' }}"
        
    - name: Notify Failure
      if: needs.web-build-test.result == 'failure' || needs.ios-build.result == 'failure'
      run: |
        echo "❌ iOS CI/CD Pipeline failed!"
        echo "Web Build Status: ${{ needs.web-build-test.result }}"
        echo "iOS Build Status: ${{ needs.ios-build.result }}"
        exit 1
